#ifndef _HW_JY901S_H_
#define _HW_JY901S_H_

#include "../ti_msp_dl_config.h"

// 调试开关定义
#define GYRO_DEBUG 0  // 设置为1启用调试输出，0禁用

#define delay_us(__us)  delay_cycles((CPUCLK_FREQ/1000000)*__us)
#define delay_ms(__ms)  delay_cycles((CPUCLK_FREQ/1000)*__ms)

/********************************I2C***************************************/
//设置SDA输出模式
#define SDA_OUT()   {                                                  \
                        DL_GPIO_initDigitalOutput(GYRO_I2C_SDA_IOMUX);     \
                        DL_GPIO_setPins(GYRO_I2C_SDA_PORT, GYRO_I2C_SDA_PIN);      \
                        DL_GPIO_enableOutput(GYRO_I2C_SDA_PORT, GYRO_I2C_SDA_PIN); \
                    }
//设置SDA输入模式
#define SDA_IN()    { DL_GPIO_initDigitalInput(GYRO_I2C_SDA_IOMUX); }

//获取SDA引脚的电平变化
#define SDA_GET()   ( ( ( DL_GPIO_readPins(GYRO_I2C_SDA_PORT,GYRO_I2C_SDA_PIN) & GYRO_I2C_SDA_PIN ) > 0 ) ? 1 : 0 )
//SDA与SCL输出
#define SDA(x)      ( (x) ? (DL_GPIO_setPins(GYRO_I2C_SDA_PORT,GYRO_I2C_SDA_PIN)) : (DL_GPIO_clearPins(GYRO_I2C_SDA_PORT,GYRO_I2C_SDA_PIN)) )
#define SCL(x)      ( (x) ? (DL_GPIO_setPins(GYRO_I2C_SCL_PORT,GYRO_I2C_SCL_PIN)) : (DL_GPIO_clearPins(GYRO_I2C_SCL_PORT,GYRO_I2C_SCL_PIN)) )
/***********************************************************************/

/********************************jy901s的i2c寄存器地址***************************************/
#define JY901S_ADDRESS		0X50//0xa0

#define SAVE 			0x00
#define CALSW 		0x01
#define RSW 			0x02
#define RRATE			0x03
#define BAUD 			0x04
#define AXOFFSET	0x05
#define AYOFFSET	0x06
#define AZOFFSET	0x07
#define GXOFFSET	0x08
#define GYOFFSET	0x09
#define GZOFFSET	0x0a
#define HXOFFSET	0x0b
#define HYOFFSET	0x0c
#define HZOFFSET	0x0d
#define D0MODE		0x0e
#define D1MODE		0x0f
#define D2MODE		0x10
#define D3MODE		0x11
#define D0PWMH		0x12
#define D1PWMH		0x13
#define D2PWMH		0x14
#define D3PWMH		0x15
#define D0PWMT		0x16
#define D1PWMT		0x17
#define D2PWMT		0x18
#define D3PWMT		0x19
#define IICADDR		0x1a
#define LEDOFF 		0x1b
#define GPSBAUD		0x1c
 
#define YYMM				0x30
#define DDHH				0x31
#define MMSS				0x32
#define MS					0x33
#define AX					0x34
#define AY					0x35
#define AZ					0x36
#define GX					0x37
#define GY					0x38
#define GZ					0x39
#define HX					0x3a
#define HY					0x3b
#define HZ					0x3c			
#define ROLL				0x3d    //Roll的数据地址
#define Pitch				0x3e
#define Yaw					0x3f
#define TEMP				0x40
#define D0Status		0x41
#define D1Status		0x42
#define D2Status		0x43
#define D3Status		0x44
#define PressureL		0x45
#define PressureH		0x46
#define HeightL			0x47
#define HeightH			0x48
#define LonL				0x49
#define LonH				0x4a
#define LatL				0x4b
#define LatH				0x4c
#define GPSHeight   0x4d
#define GPSYAW      0x4e
#define GPSVL				0x4f
#define GPSVH				0x50
      
#define DIO_MODE_AIN 0
#define DIO_MODE_DIN 1
#define DIO_MODE_DOH 2
#define DIO_MODE_DOL 3
#define DIO_MODE_DOPWM 4
#define DIO_MODE_GPS 5	

// 航向角地址
#define YAW_REG_ADDR	0x3F
// 寄存器解锁
#define UN_REG			0x69
// 保存寄存器
#define SAVE_REG		0x00
// 角度参考寄存器
#define ANGLE_REFER_REG	0x01
/***********************************************************************/

// 定义一个结构体来存储
typedef struct {
    float x;
    float y;
    float z;
}GYRO_ANGLE_DATA_STRUCT;



void jy901s_init(void);
char jy901s_write_reg(uint8_t addr, uint8_t regaddr, uint8_t num, uint8_t* regdata);
char jy901s_read_data(uint8_t addr, uint8_t regaddr,uint8_t num,uint8_t* Read);
GYRO_ANGLE_DATA_STRUCT get_angle(void);
#endif
