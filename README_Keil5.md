# JY901S Keil5 项目配置指南

## 项目概述

本项目已经配置为支持Keil MDK5开发环境，用于MSPM0G3507微控制器的JY901S陀螺仪传感器驱动，并集成了SSD1306 OLED显示屏，能够实时显示Roll、Pitch、Yaw三轴姿态角度数据。

## 硬件配置

### 引脚分配
- **JY901S SDA**: PB9 (PORTB, Pin 9)
- **JY901S SCL**: PA18 (PORTA, Pin 18)
- **OLED SDA**: PA12 (PORTA, Pin 12)
- **OLED SCL**: PA13 (PORTA, Pin 13)
- **调试接口**: PA19 (SWDIO), PA20 (SWCLK)

### 目标硬件
- 微控制器: MSPM0G3507
- 开发板: LP-MSPM0G3507 LaunchPad
- 传感器: JY901S 9轴陀螺仪
- 显示屏: 0.96寸OLED (SSD1306控制器)

## Keil5 环境配置

### 1. 安装必要的软件包

在Keil5中，您需要安装以下Device Pack：

```
Texas Instruments MSPM0G1X0X_G3X0X Series Device Family Pack
```

安装步骤：
1. 打开Keil μVision5
2. 点击 `Pack Installer`
3. 搜索 "MSPM0G3507"
4. 安装 Texas Instruments 的设备包

### 2. 项目文件说明

#### 核心文件
- `jy901s.uvprojx` - Keil5项目文件
- `empty.c` - 主程序文件
- `ti_msp_dl_config.h/c` - 硬件配置文件
- `system_mspm0g3507.c` - 系统初始化文件
- `startup_mspm0g3507.s` - 启动文件

#### 驱动文件
- `hardware/hw_jy901s.h/c` - JY901S传感器驱动
- `hardware/hw_ssd1306.h/c` - SSD1306 OLED显示驱动
- `hardware/hw_fonts.h` - 8x16像素字体数据

### 3. 编译配置

项目已配置以下编译选项：
- 编译器: ARM Compiler 6 (ARMCLANG)
- 优化级别: -O1
- 目标处理器: Cortex-M0+
- 预定义宏: `__MSPM0G3507__`

### 4. 调试配置

- 调试器: CMSIS-DAP (XDS110)
- 接口: SWD
- 时钟频率: 1MHz

## 使用说明

### 1. 打开项目
1. 启动Keil μVision5
2. 打开 `jy901s.uvprojx` 项目文件

### 2. 编译项目
1. 点击 `Project` -> `Build Target` 或按 F7
2. 确保编译无错误

### 3. 下载和调试
1. 连接LP-MSPM0G3507开发板到PC
2. 点击 `Flash` -> `Download` 或按 F8
3. 启动调试会话

### 4. 观察运行结果
程序运行后，您可以：
1. **OLED实时显示**: 观察OLED屏幕上实时显示的Roll、Pitch、Yaw角度数据
2. **调试模式**: 在调试模式下观察 `attitude` 结构体变量的值
3. **断点调试**: 在 `main()` 函数的 `attitude = get_angle();` 行设置断点
4. **Watch窗口**: 通过Watch窗口监控角度值变化

#### OLED显示格式
```
   Attitude Data

Roll:  -12.5 deg
Pitch:  45.2 deg
Yaw:   180.0 deg
```

## 硬件连接

### 完整连接方案
请参考 `硬件连接说明.md` 文件获取详细的硬件连接指南。

### 快速连接表
| 器件 | 引脚 | LP-MSPM0G3507 | 说明 |
|------|------|---------------|------|
| JY901S | VCC | 3.3V | 电源正极 |
| JY901S | GND | GND | 电源负极 |
| JY901S | SDA | PB9 | I2C数据线 |
| JY901S | SCL | PA18 | I2C时钟线 |
| OLED | VCC | 3.3V | 电源正极 |
| OLED | GND | GND | 电源负极 |
| OLED | SDA | PA12 | I2C数据线 |
| OLED | SCL | PA13 | I2C时钟线 |

### 注意事项
1. **电源连接**: 确保所有器件共用3.3V电源和GND
2. **I2C总线**: JY901S和OLED使用不同的I2C总线，避免地址冲突
3. **线缆长度**: 建议I2C连线长度不超过20cm
4. **连接顺序**: 断电状态下进行所有连接

## 与TI CCS项目的差异

### 主要差异
1. **SysConfig支持**: Keil5不支持TI SysConfig工具，因此手动创建了配置文件
2. **编译器**: 使用ARM Compiler 6替代TI CLANG
3. **启动文件**: 使用标准ARM启动文件格式
4. **调试输出**: 移除了printf调用以简化配置
5. **DriverLib**: 使用简化的GPIO操作函数替代完整的TI DriverLib

### 功能保持
- JY901S I2C通信协议完全兼容
- 引脚配置保持一致 (PB9-SDA, PA18-SCL)
- 传感器初始化和数据读取功能不变

### 重要说明
**当前版本是一个基础框架**，为了在Keil5中编译通过，我们创建了简化的GPIO操作函数。要获得完整功能，您需要：

1. **安装TI MSPM0 Device Pack**: 从Keil官网或TI官网下载完整的设备支持包
2. **更新DriverLib路径**: 在ti_msp_dl_config.h中启用正确的TI DriverLib包含
3. **完善GPIO配置**: 根据实际的DriverLib API完善GPIO和时钟配置

### 完整配置步骤
1. 下载并安装 `TexasInstruments.MSPM0G1X0X_G3X0X_DFP.x.x.x.pack`
2. 在项目设置中添加正确的Include路径
3. 替换简化的GPIO函数为完整的TI DriverLib调用

## 故障排除

### 常见问题

1. **编译错误: 找不到头文件**
   - 确保安装了MSPM0G3507设备包
   - 检查Include路径设置

2. **下载失败**
   - 检查开发板连接
   - 确认调试器配置正确

3. **程序不运行**
   - 检查启动文件配置
   - 验证时钟配置

### 调试技巧

1. 使用Keil的Logic Analyzer观察I2C信号
2. 在关键函数设置断点
3. 使用Watch窗口监控变量值

## 注意事项

1. 本项目针对Keil5进行了优化，可能不直接兼容其他IDE
2. 如需printf输出，需要配置串口重定向
3. 确保硬件连接正确，特别是I2C引脚

## 技术支持

如有问题，请检查：
1. MSPM0G3507数据手册
2. LP-MSPM0G3507用户指南
3. JY901S传感器文档
