/*****************************************************************************
 * @file     system_mspm0g3507.c
 * @brief    CMSIS Cortex-M0+ Device System Source File for MSPM0G3507
 * @version  V1.0.0
 * @date     2024-06-29
 ******************************************************************************/

#include <stdint.h>
#include "ti_msp_dl_config.h"

/*----------------------------------------------------------------------------
  Define clocks
 *----------------------------------------------------------------------------*/
#define __SYSTEM_CLOCK    (32000000UL)

/*----------------------------------------------------------------------------
  System Core Clock Variable
 *----------------------------------------------------------------------------*/
uint32_t SystemCoreClock = __SYSTEM_CLOCK;  /* System Core Clock Frequency */

/*----------------------------------------------------------------------------
  System Core Clock update function
 *----------------------------------------------------------------------------*/
void SystemCoreClockUpdate (void)
{
    SystemCoreClock = __SYSTEM_CLOCK;
}

/*----------------------------------------------------------------------------
  System initialization function
 *----------------------------------------------------------------------------*/
void SystemInit (void)
{
    SystemCoreClock = __SYSTEM_CLOCK;
}
