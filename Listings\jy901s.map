Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to empty.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    empty.o(.text.main) refers to empty.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    empty.o(.text.main) refers to hw_jy901s.o(.text.jy901s_init) for jy901s_init
    empty.o(.text.main) refers to hw_ssd1306.o(.text.oled_init) for oled_init
    empty.o(.text.main) refers to hw_jy901s.o(.text.get_angle) for get_angle
    empty.o(.text.main) refers to hw_ssd1306.o(.text.oled_display_attitude) for oled_display_attitude
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_GPIO_setPins) refers to empty.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    empty.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to empty.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_start) for i2c_start
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_send_byte) for i2c_send_byte
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_wait_ack) for i2c_wait_ack
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.ARM.exidx.text.jy901s_write_reg) refers to hw_jy901s.o(.text.jy901s_write_reg) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_start) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_start) refers to hw_jy901s.o(.text.i2c_start) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_send_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_send_byte) refers to hw_jy901s.o(.text.i2c_send_byte) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.ARM.exidx.text.i2c_wait_ack) refers to hw_jy901s.o(.text.i2c_wait_ack) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_stop) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_stop) refers to hw_jy901s.o(.text.i2c_stop) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_start) for i2c_start
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_send_byte) for i2c_send_byte
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_wait_ack) for i2c_wait_ack
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.text.jy901s_read_data) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_read_byte) for i2c_read_byte
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_send_ack) for i2c_send_ack
    hw_jy901s.o(.ARM.exidx.text.jy901s_read_data) refers to hw_jy901s.o(.text.jy901s_read_data) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_read_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_jy901s.o(.ARM.exidx.text.i2c_read_byte) refers to hw_jy901s.o(.text.i2c_read_byte) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_send_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_send_ack) refers to hw_jy901s.o(.text.i2c_send_ack) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_init) refers to hw_jy901s.o(.text.jy901s_write_reg) for jy901s_write_reg
    hw_jy901s.o(.text.jy901s_init) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.jy901s_init) refers to hw_jy901s.o(.text.jy901s_init) for [Anonymous Symbol]
    hw_jy901s.o(.text.get_angle) refers to hw_jy901s.o(.text.jy901s_read_data) for jy901s_read_data
    hw_jy901s.o(.text.get_angle) refers to dflti.o(.text) for __aeabi_i2d
    hw_jy901s.o(.text.get_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    hw_jy901s.o(.text.get_angle) refers to dmul.o(.text) for __aeabi_dmul
    hw_jy901s.o(.text.get_angle) refers to d2f.o(.text) for __aeabi_d2f
    hw_jy901s.o(.text.get_angle) refers to f2d.o(.text) for __aeabi_f2d
    hw_jy901s.o(.text.get_angle) refers to dcmp.o(i._dleq) for __aeabi_dcmple
    hw_jy901s.o(.text.get_angle) refers to daddsub.o(.text) for __aeabi_dadd
    hw_jy901s.o(.text.get_angle) refers to dcmp.o(i._dgeq) for __aeabi_dcmpge
    hw_jy901s.o(.text.get_angle) refers to hw_jy901s.o(.bss.angle_struct) for angle_struct
    hw_jy901s.o(.ARM.exidx.text.get_angle) refers to hw_jy901s.o(.text.get_angle) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_start) for oled_i2c_start
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_send_byte) for oled_i2c_send_byte
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_wait_ack) for oled_i2c_wait_ack
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_stop) for oled_i2c_stop
    hw_ssd1306.o(.ARM.exidx.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_write_cmd) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_ssd1306.o(.text.oled_i2c_start) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_start) refers to hw_ssd1306.o(.text.oled_i2c_start) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.oled_i2c_send_byte) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.oled_i2c_stop) for oled_i2c_stop
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.oled_i2c_wait_ack) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.text.oled_i2c_stop) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.oled_i2c_stop) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_start) for oled_i2c_start
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_send_byte) for oled_i2c_send_byte
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_wait_ack) for oled_i2c_wait_ack
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_stop) for oled_i2c_stop
    hw_ssd1306.o(.ARM.exidx.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_write_data) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_init) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_write_cmd) for oled_write_cmd
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_clear) for oled_clear
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_set_pixel) for oled_set_pixel
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_update_display) for oled_update_display
    hw_ssd1306.o(.ARM.exidx.text.oled_init) refers to hw_ssd1306.o(.text.oled_init) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_clear) refers to rt_memclr.o(.text) for __aeabi_memclr
    hw_ssd1306.o(.text.oled_clear) refers to hw_ssd1306.o(.bss.oled_buffer) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_clear) refers to hw_ssd1306.o(.text.oled_clear) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_set_pixel) refers to hw_ssd1306.o(.bss.oled_buffer) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_set_pixel) refers to hw_ssd1306.o(.text.oled_set_pixel) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_update_display) refers to hw_ssd1306.o(.text.oled_write_cmd) for oled_write_cmd
    hw_ssd1306.o(.text.oled_update_display) refers to hw_ssd1306.o(.text.oled_write_data) for oled_write_data
    hw_ssd1306.o(.text.oled_update_display) refers to hw_ssd1306.o(.bss.oled_buffer) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_update_display) refers to hw_ssd1306.o(.text.oled_update_display) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_draw_char) refers to hw_ssd1306.o(.text.oled_set_pixel) for oled_set_pixel
    hw_ssd1306.o(.text.oled_draw_char) refers to hw_ssd1306.o(.rodata.font8x16) for font8x16
    hw_ssd1306.o(.ARM.exidx.text.oled_draw_char) refers to hw_ssd1306.o(.text.oled_draw_char) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_draw_string) refers to hw_ssd1306.o(.text.oled_draw_char) for oled_draw_char
    hw_ssd1306.o(.ARM.exidx.text.oled_draw_string) refers to hw_ssd1306.o(.text.oled_draw_string) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_draw_number) refers to fcmp.o(i._fgeq) for __aeabi_fcmpge
    hw_ssd1306.o(.text.oled_draw_number) refers to ffixi.o(.text) for __aeabi_f2iz
    hw_ssd1306.o(.text.oled_draw_number) refers to aeabi_sdiv.o(.text) for __aeabi_idivmod
    hw_ssd1306.o(.text.oled_draw_number) refers to fflti.o(.text) for __aeabi_i2f
    hw_ssd1306.o(.text.oled_draw_number) refers to faddsub.o(x$fpl$fsub) for __aeabi_fsub
    hw_ssd1306.o(.text.oled_draw_number) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    hw_ssd1306.o(.text.oled_draw_number) refers to hw_ssd1306.o(.text.oled_draw_string) for oled_draw_string
    hw_ssd1306.o(.ARM.exidx.text.oled_draw_number) refers to hw_ssd1306.o(.text.oled_draw_number) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_clear) for oled_clear
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_draw_string) for oled_draw_string
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_draw_number) for oled_draw_number
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_update_display) for oled_update_display
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.rodata.str1.1) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_display_attitude) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_ssd1306.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.delay_cycles) refers to ti_msp_dl_config.o(.text.delay_cycles) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    system_mspm0g3507.o(.text.SystemCoreClockUpdate) refers to system_mspm0g3507.o(.data.SystemCoreClock) for SystemCoreClock
    system_mspm0g3507.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_mspm0g3507.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_mspm0g3507.o(.text.SystemInit) refers to system_mspm0g3507.o(.data.SystemCoreClock) for SystemCoreClock
    system_mspm0g3507.o(.ARM.exidx.text.SystemInit) refers to system_mspm0g3507.o(.text.SystemInit) for [Anonymous Symbol]
    startup_mspm0g3507.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g3507.o(RESET) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    startup_mspm0g3507.o(RESET) refers to startup_mspm0g3507.o(.text) for Reset_Handler
    startup_mspm0g3507.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g3507.o(.text) refers to system_mspm0g3507.o(.text.SystemInit) for SystemInit
    startup_mspm0g3507.o(.text) refers to __main.o(!!!main) for __main
    startup_mspm0g3507.o(.text) refers to startup_mspm0g3507.o(HEAP) for Heap_Mem
    startup_mspm0g3507.o(.text) refers to startup_mspm0g3507.o(STACK) for Stack_Mem
    startup_mspm0g3507.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_mspm0g3507.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__eqdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__eqdf2) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i.__gedf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__gedf2) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i.__gtdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__gtdf2) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i.__ledf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__ledf2) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i.__ltdf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__ltdf2) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i.__nedf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i.__nedf2) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._deq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._deq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(i._dgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgeq) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dgr) refers to dgef.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(i._dleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dleq) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dls) refers to dlef.o(x$fpl$dleqf) for _dcmple
    dcmp.o(i._dneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(i._dneq) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    ddiv.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(.text) refers to ddiv.o(.constdata) for .constdata
    ddiv.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fadd) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fsub) for _fsub1
    faddsub.o(x$fpl$frsb) refers to faddsub.o(x$fpl$fadd) for _fadd1
    faddsub.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub.o(x$fpl$fsub) refers to faddsub.o(x$fpl$fadd) for _fadd1
    fcmp.o(i.__eqsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__eqsf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i.__gesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gesf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__gtsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__gtsf2) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i.__lesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__lesf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__ltsf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__ltsf2) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i.__nesf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i.__nesf2) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._feq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._feq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(i._fgeq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgeq) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fgr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fgr) refers to fgef.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(i._fleq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fleq) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fls) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fls) refers to flef.o(x$fpl$fleqf) for _fcmple
    fcmp.o(i._fneq) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(i._fneq) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    ffixi.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflti.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry5.o(.ARM.Collect$$rtentry$$00000005) for __rt_entry_sh
    aeabi_idiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dgef.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgef.o(x$fpl$dgeqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    dlef.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dlef.o(x$fpl$dleqf) refers to dcmpin.o(.text) for __fpl_dcmp_InfNaN
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    fgef.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgef.o(x$fpl$fgeqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    flef.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flef.o(x$fpl$fleqf) refers to fcmpin.o(.text) for __fpl_fcmp_InfNaN
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to empty.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    __rtentry5.o(.ARM.Collect$$rtentry$$00000005) refers to __rtentry5.o(.ARM.Collect$$rtentry$$00002716) for __lit__00000000
    __rtentry5.o(.ARM.Collect$$rtentry$$00002716) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    __rtentry5.o(__vectab_stack_and_reset_sym_area) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    __rtentry5.o(__vectab_stack_and_reset_sym_area) refers to __main.o(!!!main) for __main
    __rtentry5.o(.ARM.exidx) refers to __rtentry5.o(.ARM.Collect$$rtentry$$00000005) for .ARM.Collect$$rtentry$$00000005
    __rtentry5.o(.ARM.exidx) refers to __rtentry5.o(.ARM.Collect$$rtentry$$00002716) for .ARM.Collect$$rtentry$$00002716
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    dcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    dcmpin.o(.text) refers to dnan2.o(.text) for __fpl_dcheck_NaN2
    fcmpin.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpin.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    fcmpin.o(.text) refers to fnan2.o(.text) for __fpl_fcheck_NaN2
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_mspm0g3507.o(.text) for __user_initial_stackheap
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to tempstk.o(.text) for __temporary_stack_top
    sys_stackheap_outer.o(__vectab_stack_and_reset_area) refers to __main.o(!!!main) for __main
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    cmpret.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    fnan2.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnan2.o(.text) refers to retnan.o(.text) for __fpl_return_NaN
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.text) for __ARM_argv_veneer
    retnan.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(.text) refers to cmpret.o(.text) for __fpl_cmpreturn
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing empty.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing hw_jy901s.o(.text), (0 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_write_reg), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_start), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_send_byte), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_wait_ack), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_stop), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_read_data), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_read_byte), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_send_ack), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_init), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.get_angle), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing hw_jy901s.o(.rodata..L__const.jy901s_init.unlock_reg1), (2 bytes).
    Removing hw_jy901s.o(.rodata.str1.1), (4 bytes).
    Removing hw_jy901s.o(.rodata..L__const.jy901s_init.unlock_reg), (2 bytes).
    Removing hw_ssd1306.o(.text), (0 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_write_cmd), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_start), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_send_byte), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_wait_ack), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_stop), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_write_data), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_init), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_clear), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_set_pixel), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_update_display), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_draw_char), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_draw_string), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_draw_number), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_display_attitude), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.delay_cycles), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing system_mspm0g3507.o(.text), (0 bytes).
    Removing system_mspm0g3507.o(.text.SystemCoreClockUpdate), (16 bytes).
    Removing system_mspm0g3507.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_mspm0g3507.o(.ARM.exidx.text.SystemInit), (8 bytes).

62 unused section(s) (total 444 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry5.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_idiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatterp.s                 0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  tempstk.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_div0.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.c                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  dcmp.o ABSOLUTE
    ../fplib/cfplib/cmp.c                    0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/cfplib/cmpret.c                 0x00000000   Number         0  cmpret.o ABSOLUTE
    ../fplib/cfplib/d2f.c                    0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/cfplib/daddsub.c                0x00000000   Number         0  daddsub.o ABSOLUTE
    ../fplib/cfplib/dcmpin.c                 0x00000000   Number         0  dcmpin.o ABSOLUTE
    ../fplib/cfplib/ddiv.c                   0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/cfplib/dmul.c                   0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/cfplib/f2d.c                    0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/cfplib/fcmpin.c                 0x00000000   Number         0  fcmpin.o ABSOLUTE
    ../fplib/cfplib/ffix.c                   0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/cfplib/fflt.c                   0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/cfplib/fpinit.c                 0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  dnan2.o ABSOLUTE
    ../fplib/cfplib/nan2.c                   0x00000000   Number         0  fnan2.o ABSOLUTE
    ../fplib/cfplib/retnan.c                 0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/deqf6m.s                        0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dgeqf6m.s                       0x00000000   Number         0  dgef.o ABSOLUTE
    ../fplib/dleqf6m.s                       0x00000000   Number         0  dlef.o ABSOLUTE
    ../fplib/faddsub6m.s                     0x00000000   Number         0  faddsub.o ABSOLUTE
    ../fplib/feqf6m.s                        0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fgeqf6m.s                       0x00000000   Number         0  fgef.o ABSOLUTE
    ../fplib/fleqf6m.s                       0x00000000   Number         0  flef.o ABSOLUTE
    ../fplib/fmul6m.s                        0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    hw_jy901s.c                              0x00000000   Number         0  hw_jy901s.o ABSOLUTE
    hw_ssd1306.c                             0x00000000   Number         0  hw_ssd1306.o ABSOLUTE
    startup_mspm0g3507.s                     0x00000000   Number         0  startup_mspm0g3507.o ABSOLUTE
    system_mspm0g3507.c                      0x00000000   Number         0  system_mspm0g3507.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    !!!scatter                               0x00000000   Section       84  __scatter.o(!!!scatter)
    !!handler_copy                           0x00000000   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x00000000   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x00000000   Section       28  __scatter_zi.o(!!handler_zi)
    RESET                                    0x00000000   Section      132  startup_mspm0g3507.o(RESET)
    !!!main                                  0x00000084   Section        8  __main.o(!!!main)
    .ARM.Collect$$libinit$$00000000          0x0000008c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x0000008e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x0000008e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x00000090   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x00000092   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x00000092   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x00000092   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x00000092   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x00000092   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x00000092   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x00000092   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x00000094   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x00000094   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000005          0x00000094   Section        4  __rtentry5.o(.ARM.Collect$$rtentry$$00000005)
    .ARM.Collect$$rtentry$$00000009          0x00000098   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x00000098   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0000009c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0000009c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __lit__00000000                          0x000000a4   Data           4  __rtentry5.o(.ARM.Collect$$rtentry$$00002716)
    .ARM.Collect$$rtentry$$00002716          0x000000a4   Section        4  __rtentry5.o(.ARM.Collect$$rtentry$$00002716)
    .ARM.Collect$$rtexit$$00000000           0x000000a8   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x000000aa   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x000000aa   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x000000ae   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x000000b4   Section       56  startup_mspm0g3507.o(.text)
    .text                                    0x000000ec   Section        0  rt_memclr.o(.text)
    .text                                    0x0000012c   Section      364  aeabi_sdiv.o(.text)
    .text                                    0x00000298   Section        0  heapauxi.o(.text)
    .text                                    0x000002a0   Section        0  d2f.o(.text)
    _dadd1                                   0x0000031d   Thumb Code   290  daddsub.o(.text)
    .text                                    0x0000031c   Section        0  daddsub.o(.text)
    _dsub1                                   0x0000043f   Thumb Code   470  daddsub.o(.text)
    .text                                    0x00000674   Section        0  ddiv.o(.text)
    .text                                    0x00000abc   Section        0  dflti.o(.text)
    .text                                    0x00000b14   Section        0  dmul.o(.text)
    .text                                    0x00000d5c   Section        0  f2d.o(.text)
    .text                                    0x00000db0   Section        0  ffixi.o(.text)
    .text                                    0x00000dfc   Section        0  fflti.o(.text)
    .text                                    0x00000e5c   Section        0  dcmpin.o(.text)
    .text                                    0x00000efc   Section        0  fcmpin.o(.text)
    .text                                    0x00000f60   Section        0  exit.o(.text)
    .text                                    0x00000f70   Section        0  cmpret.o(.text)
    .text                                    0x00000fa0   Section        0  dnan2.o(.text)
    .text                                    0x00000fb4   Section        0  fnan2.o(.text)
    .text                                    0x00000fc4   Section        0  retnan.o(.text)
    .text                                    0x00001024   Section        0  sys_exit.o(.text)
    .text                                    0x00001030   Section        2  use_no_semi.o(.text)
    DL_GPIO_clearPins                        0x00001033   Thumb Code    16  empty.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001032   Section        0  empty.o(.text.DL_GPIO_clearPins)
    .text                                    0x00001032   Section        0  indicate_semi.o(.text)
    DL_GPIO_clearPins                        0x00001043   Thumb Code    16  hw_jy901s.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001042   Section        0  hw_jy901s.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00001053   Thumb Code    16  hw_ssd1306.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001052   Section        0  hw_ssd1306.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x00001063   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00001062   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableOutput                     0x00001073   Thumb Code    20  hw_jy901s.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00001072   Section        0  hw_jy901s.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enableOutput                     0x00001087   Thumb Code    20  hw_ssd1306.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x00001086   Section        0  hw_ssd1306.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enableOutput                     0x0000109b   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x0000109a   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x000010af   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x000010ae   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_initDigitalInput                 0x000010b7   Thumb Code     8  hw_jy901s.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x000010b6   Section        0  hw_jy901s.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalInput                 0x000010bf   Thumb Code     8  hw_ssd1306.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x000010be   Section        0  hw_ssd1306.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalOutput                0x000010c7   Thumb Code     8  hw_jy901s.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000010c6   Section        0  hw_jy901s.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutput                0x000010cf   Thumb Code     8  hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000010ce   Section        0  hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutput                0x000010d7   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000010d6   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_readPins                         0x000010df   Thumb Code    18  hw_jy901s.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x000010de   Section        0  hw_jy901s.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x000010f1   Thumb Code    18  hw_ssd1306.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x000010f0   Section        0  hw_ssd1306.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00001103   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00001102   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setPins                          0x0000110b   Thumb Code    16  empty.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000110a   Section        0  empty.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x0000111b   Thumb Code    16  hw_jy901s.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000111a   Section        0  hw_jy901s.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x0000112b   Thumb Code    16  hw_ssd1306.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000112a   Section        0  hw_ssd1306.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x0000113b   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000113a   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000114c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.3_0                             0x000011dc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000011e0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000011e2   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x000011f4   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x00001220   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00001224   Section        0  system_mspm0g3507.o(.text.SystemInit)
    __arm_cp.1_0                             0x0000122c   Number         4  system_mspm0g3507.o(.text.SystemInit)
    __arm_cp.1_1                             0x00001230   Number         4  system_mspm0g3507.o(.text.SystemInit)
    [Anonymous Symbol]                       0x00001234   Section        0  ti_msp_dl_config.o(.text.delay_cycles)
    [Anonymous Symbol]                       0x00001250   Section        0  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_0                             0x0000141c   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_1                             0x00001420   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_2                             0x00001424   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_3                             0x00001428   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_4                             0x0000142c   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_5                             0x00001430   Number         4  hw_jy901s.o(.text.get_angle)
    i2c_read_byte                            0x00001435   Thumb Code   164  hw_jy901s.o(.text.i2c_read_byte)
    [Anonymous Symbol]                       0x00001434   Section        0  hw_jy901s.o(.text.i2c_read_byte)
    __arm_cp.6_0                             0x000014d8   Number         4  hw_jy901s.o(.text.i2c_read_byte)
    i2c_send_ack                             0x000014dd   Thumb Code   144  hw_jy901s.o(.text.i2c_send_ack)
    [Anonymous Symbol]                       0x000014dc   Section        0  hw_jy901s.o(.text.i2c_send_ack)
    __arm_cp.7_0                             0x0000156c   Number         4  hw_jy901s.o(.text.i2c_send_ack)
    i2c_send_byte                            0x00001571   Thumb Code   168  hw_jy901s.o(.text.i2c_send_byte)
    [Anonymous Symbol]                       0x00001570   Section        0  hw_jy901s.o(.text.i2c_send_byte)
    __arm_cp.2_0                             0x00001618   Number         4  hw_jy901s.o(.text.i2c_send_byte)
    i2c_start                                0x0000161d   Thumb Code   104  hw_jy901s.o(.text.i2c_start)
    [Anonymous Symbol]                       0x0000161c   Section        0  hw_jy901s.o(.text.i2c_start)
    __arm_cp.1_0                             0x00001684   Number         4  hw_jy901s.o(.text.i2c_start)
    i2c_stop                                 0x00001689   Thumb Code    92  hw_jy901s.o(.text.i2c_stop)
    [Anonymous Symbol]                       0x00001688   Section        0  hw_jy901s.o(.text.i2c_stop)
    __arm_cp.4_0                             0x000016e4   Number         4  hw_jy901s.o(.text.i2c_stop)
    i2c_wait_ack                             0x000016e9   Thumb Code   200  hw_jy901s.o(.text.i2c_wait_ack)
    [Anonymous Symbol]                       0x000016e8   Section        0  hw_jy901s.o(.text.i2c_wait_ack)
    __arm_cp.3_0                             0x000017b0   Number         4  hw_jy901s.o(.text.i2c_wait_ack)
    [Anonymous Symbol]                       0x000017b4   Section        0  hw_jy901s.o(.text.jy901s_init)
    __arm_cp.8_0                             0x00001848   Number         4  hw_jy901s.o(.text.jy901s_init)
    __arm_cp.8_1                             0x0000184c   Number         4  hw_jy901s.o(.text.jy901s_init)
    [Anonymous Symbol]                       0x00001850   Section        0  hw_jy901s.o(.text.jy901s_read_data)
    [Anonymous Symbol]                       0x0000192c   Section        0  hw_jy901s.o(.text.jy901s_write_reg)
    [Anonymous Symbol]                       0x000019e0   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00001aa8   Number         4  empty.o(.text.main)
    __arm_cp.0_1                             0x00001aac   Number         4  empty.o(.text.main)
    __arm_cp.0_2                             0x00001ab0   Number         4  empty.o(.text.main)
    __arm_cp.0_3                             0x00001ab4   Number         4  empty.o(.text.main)
    __arm_cp.0_4                             0x00001ab8   Number         4  empty.o(.text.main)
    __arm_cp.0_5                             0x00001abc   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00001ac0   Section        0  hw_ssd1306.o(.text.oled_clear)
    __arm_cp.7_0                             0x00001ad0   Number         4  hw_ssd1306.o(.text.oled_clear)
    [Anonymous Symbol]                       0x00001ad4   Section        0  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_0                            0x00001b68   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_1                            0x00001b6c   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_2                            0x00001b70   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_3                            0x00001b74   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_4                            0x00001b78   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    [Anonymous Symbol]                       0x00001b7c   Section        0  hw_ssd1306.o(.text.oled_draw_char)
    __arm_cp.10_0                            0x00001c50   Number         4  hw_ssd1306.o(.text.oled_draw_char)
    [Anonymous Symbol]                       0x00001c54   Section        0  hw_ssd1306.o(.text.oled_draw_number)
    __arm_cp.12_0                            0x00001db8   Number         4  hw_ssd1306.o(.text.oled_draw_number)
    [Anonymous Symbol]                       0x00001dbc   Section        0  hw_ssd1306.o(.text.oled_draw_string)
    oled_i2c_send_byte                       0x00001e11   Thumb Code   168  hw_ssd1306.o(.text.oled_i2c_send_byte)
    [Anonymous Symbol]                       0x00001e10   Section        0  hw_ssd1306.o(.text.oled_i2c_send_byte)
    oled_i2c_start                           0x00001eb9   Thumb Code    88  hw_ssd1306.o(.text.oled_i2c_start)
    [Anonymous Symbol]                       0x00001eb8   Section        0  hw_ssd1306.o(.text.oled_i2c_start)
    oled_i2c_stop                            0x00001f11   Thumb Code    94  hw_ssd1306.o(.text.oled_i2c_stop)
    [Anonymous Symbol]                       0x00001f10   Section        0  hw_ssd1306.o(.text.oled_i2c_stop)
    oled_i2c_wait_ack                        0x00001f6f   Thumb Code   136  hw_ssd1306.o(.text.oled_i2c_wait_ack)
    [Anonymous Symbol]                       0x00001f6e   Section        0  hw_ssd1306.o(.text.oled_i2c_wait_ack)
    [Anonymous Symbol]                       0x00001ff8   Section        0  hw_ssd1306.o(.text.oled_init)
    __arm_cp.6_0                             0x00002114   Number         4  hw_ssd1306.o(.text.oled_init)
    __arm_cp.6_1                             0x00002118   Number         4  hw_ssd1306.o(.text.oled_init)
    __arm_cp.6_2                             0x0000211c   Number         4  hw_ssd1306.o(.text.oled_init)
    [Anonymous Symbol]                       0x00002120   Section        0  hw_ssd1306.o(.text.oled_set_pixel)
    __arm_cp.8_0                             0x00002198   Number         4  hw_ssd1306.o(.text.oled_set_pixel)
    [Anonymous Symbol]                       0x0000219c   Section        0  hw_ssd1306.o(.text.oled_update_display)
    __arm_cp.9_0                             0x000021f8   Number         4  hw_ssd1306.o(.text.oled_update_display)
    __arm_cp.9_1                             0x000021fc   Number         4  hw_ssd1306.o(.text.oled_update_display)
    [Anonymous Symbol]                       0x00002200   Section        0  hw_ssd1306.o(.text.oled_write_cmd)
    [Anonymous Symbol]                       0x00002236   Section        0  hw_ssd1306.o(.text.oled_write_data)
    i._dgeq                                  0x0000226c   Section        0  dcmp.o(i._dgeq)
    i._dleq                                  0x00002282   Section        0  dcmp.o(i._dleq)
    i._fgeq                                  0x0000229c   Section        0  fcmp.o(i._fgeq)
    x$fpl$dgeqf                              0x000022b4   Section      100  dgef.o(x$fpl$dgeqf)
    x$fpl$dleqf                              0x00002318   Section      100  dlef.o(x$fpl$dleqf)
    x$fpl$fadd                               0x0000237c   Section      140  faddsub.o(x$fpl$fadd)
    _fadd1                                   0x00002389   Thumb Code     0  faddsub.o(x$fpl$fadd)
    x$fpl$fgeqf                              0x00002408   Section       84  fgef.o(x$fpl$fgeqf)
    x$fpl$fmul                               0x0000245c   Section      176  fmul.o(x$fpl$fmul)
    x$fpl$fsub                               0x0000250c   Section      208  faddsub.o(x$fpl$fsub)
    _fsub1                                   0x00002519   Thumb Code     0  faddsub.o(x$fpl$fsub)
    ddiv_reciptbl                            0x000025dc   Data         128  ddiv.o(.constdata)
    .constdata                               0x000025dc   Section      128  ddiv.o(.constdata)
    x$fpl$usenofp                            0x000025dc   Section        0  usenofp.o(x$fpl$usenofp)
    [Anonymous Symbol]                       0x00002c5c   Section        0  hw_ssd1306.o(.rodata.str1.1)
    oled_buffer                              0x20000014   Data        1024  hw_ssd1306.o(.bss.oled_buffer)
    [Anonymous Symbol]                       0x20000014   Section        0  hw_ssd1306.o(.bss.oled_buffer)
    Heap_Mem                                 0x20000418   Data        1024  startup_mspm0g3507.o(HEAP)
    HEAP                                     0x20000418   Section     1024  startup_mspm0g3507.o(HEAP)
    Stack_Mem                                0x20000818   Data        1024  startup_mspm0g3507.o(STACK)
    STACK                                    0x20000818   Section     1024  startup_mspm0g3507.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    main                                     0x000019e1   Thumb Code   200  empty.o(.text.main)
    SYSCFG_DL_init                           0x000011e3   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    delay_cycles                             0x00001235   Thumb Code    26  ti_msp_dl_config.o(.text.delay_cycles)
    jy901s_init                              0x000017b5   Thumb Code   148  hw_jy901s.o(.text.jy901s_init)
    oled_init                                0x00001ff9   Thumb Code   284  hw_ssd1306.o(.text.oled_init)
    get_angle                                0x00001251   Thumb Code   460  hw_jy901s.o(.text.get_angle)
    oled_display_attitude                    0x00001ad5   Thumb Code   148  hw_ssd1306.o(.text.oled_display_attitude)
    jy901s_write_reg                         0x0000192d   Thumb Code   180  hw_jy901s.o(.text.jy901s_write_reg)
    jy901s_read_data                         0x00001851   Thumb Code   220  hw_jy901s.o(.text.jy901s_read_data)
    angle_struct                             0x20000008   Data          12  hw_jy901s.o(.bss.angle_struct)
    oled_write_cmd                           0x00002201   Thumb Code    54  hw_ssd1306.o(.text.oled_write_cmd)
    oled_write_data                          0x00002237   Thumb Code    54  hw_ssd1306.o(.text.oled_write_data)
    oled_clear                               0x00001ac1   Thumb Code    16  hw_ssd1306.o(.text.oled_clear)
    oled_set_pixel                           0x00002121   Thumb Code   120  hw_ssd1306.o(.text.oled_set_pixel)
    oled_update_display                      0x0000219d   Thumb Code    92  hw_ssd1306.o(.text.oled_update_display)
    oled_draw_char                           0x00001b7d   Thumb Code   212  hw_ssd1306.o(.text.oled_draw_char)
    font8x16                                 0x0000265c   Data        1536  hw_ssd1306.o(.rodata.font8x16)
    oled_draw_string                         0x00001dbd   Thumb Code    84  hw_ssd1306.o(.text.oled_draw_string)
    oled_draw_number                         0x00001c55   Thumb Code   356  hw_ssd1306.o(.text.oled_draw_number)
    SYSCFG_DL_initPower                      0x000011f5   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SYSCFG_DL_SYSCTL_init                    0x000011e1   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_GPIO_init                      0x0000114d   Thumb Code   144  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SystemCoreClock                          0x20000000   Data           4  system_mspm0g3507.o(.data.SystemCoreClock)
    SystemInit                               0x00001225   Thumb Code     8  system_mspm0g3507.o(.text.SystemInit)
    __Vectors_Size                           0x00000084   Number         0  startup_mspm0g3507.o ABSOLUTE
    __user_initial_stackheap                 0x000000c9   Thumb Code     0  startup_mspm0g3507.o(.text)
    __Vectors                                0x00000000   Data           4  startup_mspm0g3507.o(RESET)
    __Vectors_End                            0x00000084   Data           0  startup_mspm0g3507.o(RESET)
    __initial_sp                             0x20000c18   Data           0  startup_mspm0g3507.o(STACK)
    Reset_Handler                            0x000000b5   Thumb Code     8  startup_mspm0g3507.o(.text)
    NMI_Handler                              0x000000bd   Thumb Code     2  startup_mspm0g3507.o(.text)
    HardFault_Handler                        0x000000bf   Thumb Code     2  startup_mspm0g3507.o(.text)
    SVC_Handler                              0x000000c1   Thumb Code     2  startup_mspm0g3507.o(.text)
    PendSV_Handler                           0x000000c3   Thumb Code     2  startup_mspm0g3507.o(.text)
    SysTick_Handler                          0x000000c5   Thumb Code     2  startup_mspm0g3507.o(.text)
    ADC0_IRQHandler                          0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    ADC1_IRQHandler                          0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    CANFD_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    DAC_IRQHandler                           0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    GROUP0_IRQHandler                        0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    GROUP1_IRQHandler                        0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    I2C_INST_IRQHandler                      0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    SPI_INST_IRQHandler                      0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMA0_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMA1_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG0_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG12_IRQHandler                        0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG14_IRQHandler                        0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG6_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG7_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG8_IRQHandler                         0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    UART_INST_IRQHandler                     0x000000c7   Thumb Code     0  startup_mspm0g3507.o(.text)
    _memset_w                                0x000000ed   Thumb Code    26  rt_memclr.o(.text)
    _memset                                  0x00000107   Thumb Code    30  rt_memclr.o(.text)
    __aeabi_memclr                           0x00000125   Thumb Code     4  rt_memclr.o(.text)
    __rt_memclr                              0x00000125   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x00000129   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr8                          0x00000129   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr_w                            0x00000129   Thumb Code     4  rt_memclr.o(.text)
    __aeabi_uidiv                            0x0000012d   Thumb Code     0  aeabi_sdiv.o(.text)
    __aeabi_uidivmod                         0x0000012d   Thumb Code    20  aeabi_sdiv.o(.text)
    __aeabi_idiv                             0x00000141   Thumb Code     0  aeabi_sdiv.o(.text)
    __aeabi_idivmod                          0x00000141   Thumb Code   338  aeabi_sdiv.o(.text)
    __use_two_region_memory                  0x00000299   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0000029b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0000029d   Thumb Code     2  heapauxi.o(.text)
    __main                                   0x00000085   Thumb Code     8  __main.o(!!!main)
    __aeabi_d2f                              0x000002a1   Thumb Code     0  d2f.o(.text)
    __truncdfsf2                             0x000002a1   Thumb Code     0  d2f.o(.text)
    _d2f                                     0x000002a1   Thumb Code   120  d2f.o(.text)
    __adddf3                                 0x00000615   Thumb Code     0  daddsub.o(.text)
    __aeabi_dadd                             0x00000615   Thumb Code     0  daddsub.o(.text)
    _dadd                                    0x00000615   Thumb Code    26  daddsub.o(.text)
    __aeabi_dsub                             0x0000062f   Thumb Code     0  daddsub.o(.text)
    __subdf3                                 0x0000062f   Thumb Code     0  daddsub.o(.text)
    _dsub                                    0x0000062f   Thumb Code    22  daddsub.o(.text)
    __aeabi_drsub                            0x00000645   Thumb Code     0  daddsub.o(.text)
    _drsb                                    0x00000645   Thumb Code    28  daddsub.o(.text)
    __aeabi_dcmpge                           0x0000226d   Thumb Code     0  dcmp.o(i._dgeq)
    _dgeq                                    0x0000226d   Thumb Code    22  dcmp.o(i._dgeq)
    __aeabi_dcmple                           0x00002283   Thumb Code     0  dcmp.o(i._dleq)
    _dleq                                    0x00002283   Thumb Code    26  dcmp.o(i._dleq)
    __aeabi_ddiv                             0x00000675   Thumb Code     0  ddiv.o(.text)
    __divdf3                                 0x00000675   Thumb Code     0  ddiv.o(.text)
    _ddiv                                    0x00000675   Thumb Code  1072  ddiv.o(.text)
    _drdiv                                   0x00000aa5   Thumb Code    20  ddiv.o(.text)
    __aeabi_i2d_normalise                    0x00000abd   Thumb Code    66  dflti.o(.text)
    __aeabi_i2d                              0x00000aff   Thumb Code    16  dflti.o(.text)
    _dflt                                    0x00000aff   Thumb Code     0  dflti.o(.text)
    __aeabi_ui2d                             0x00000b0f   Thumb Code     6  dflti.o(.text)
    _dfltu                                   0x00000b0f   Thumb Code     0  dflti.o(.text)
    __aeabi_dmul                             0x00000b15   Thumb Code     0  dmul.o(.text)
    __muldf3                                 0x00000b15   Thumb Code     0  dmul.o(.text)
    _dmul                                    0x00000b15   Thumb Code   558  dmul.o(.text)
    __aeabi_f2d                              0x00000d5d   Thumb Code     0  f2d.o(.text)
    __extendsfdf2                            0x00000d5d   Thumb Code     0  f2d.o(.text)
    _f2d                                     0x00000d5d   Thumb Code    80  f2d.o(.text)
    __aeabi_fadd                             0x0000237d   Thumb Code     0  faddsub.o(x$fpl$fadd)
    _fadd                                    0x0000237d   Thumb Code   134  faddsub.o(x$fpl$fadd)
    __aeabi_fsub                             0x0000250d   Thumb Code     0  faddsub.o(x$fpl$fsub)
    _fsub                                    0x0000250d   Thumb Code   204  faddsub.o(x$fpl$fsub)
    __aeabi_fcmpge                           0x0000229d   Thumb Code     0  fcmp.o(i._fgeq)
    _fgeq                                    0x0000229d   Thumb Code    22  fcmp.o(i._fgeq)
    __aeabi_f2iz                             0x00000db1   Thumb Code     0  ffixi.o(.text)
    _ffix                                    0x00000db1   Thumb Code    76  ffixi.o(.text)
    __aeabi_i2f_normalise                    0x00000dfd   Thumb Code    72  fflti.o(.text)
    __aeabi_i2f                              0x00000e45   Thumb Code    16  fflti.o(.text)
    _fflt                                    0x00000e45   Thumb Code     0  fflti.o(.text)
    __aeabi_ui2f                             0x00000e55   Thumb Code     6  fflti.o(.text)
    _ffltu                                   0x00000e55   Thumb Code     0  fflti.o(.text)
    __aeabi_fmul                             0x0000245d   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x0000245d   Thumb Code   172  fmul.o(x$fpl$fmul)
    __rt_entry                               0x00000095   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    _dcmpge                                  0x000022b5   Thumb Code    94  dgef.o(x$fpl$dgeqf)
    __aeabi_cdcmple                          0x00002319   Thumb Code     0  dlef.o(x$fpl$dleqf)
    _dcmple                                  0x00002319   Thumb Code    94  dlef.o(x$fpl$dleqf)
    _fcmpge                                  0x00002409   Thumb Code    78  fgef.o(x$fpl$fgeqf)
    __I$use$fp                               0x000025dc   Number         0  usenofp.o(x$fpl$usenofp)
    __rt_entry_presh_1                       0x00000095   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_postsh_1                      0x00000099   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_li                            0x00000099   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postli_1                      0x0000009d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_entry_main                          0x0000009d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_sh                            0x00000095   Thumb Code     0  __rtentry5.o(.ARM.Collect$$rtentry$$00000005)
    __fpl_dcmp_InfNaN                        0x00000e5d   Thumb Code   154  dcmpin.o(.text)
    __fpl_fcmp_InfNaN                        0x00000efd   Thumb Code    96  fcmpin.o(.text)
    exit                                     0x00000f61   Thumb Code    16  exit.o(.text)
    _call_atexit_fns                          - Undefined Weak Reference
    __rt_lib_init                            0x0000008d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __fpl_cmpreturn                          0x00000f71   Thumb Code    46  cmpret.o(.text)
    __fpl_dcheck_NaN2                        0x00000fa1   Thumb Code    14  dnan2.o(.text)
    __fpl_fcheck_NaN2                        0x00000fb5   Thumb Code    10  fnan2.o(.text)
    __rt_exit                                0x000000a9   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_lib_init_fp_1                       0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_relocate_pie_1             0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_preinit_1                  0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_heap_1                     0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_user_alloc_1               0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_rand_1                     0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_lc_collate_1               0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_atexit_1                   0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_signal_1                   0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_fp_trap_1                  0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_clock_1                    0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_getenv_1                   0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_stdio_1                    0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_argv_1                     0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_alloca_1                   0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_exceptions_1               0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_cpp_1                      0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_return                     0x0000008f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __fpl_return_NaN                         0x00000fc5   Thumb Code    94  retnan.o(.text)
    _sys_exit                                0x00001025   Thumb Code     8  sys_exit.o(.text)
    __rt_exit_prels_1                        0x000000ab   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_ls                             0x000000ab   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_exit                           0x000000af   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    __sigvec_lookup                           - Undefined Weak Reference
    __I$use$semihosting                      0x00001031   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x00001031   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x00001033   Thumb Code     0  indicate_semi.o(.text)
    _handle_redirection                       - Undefined Weak Reference
    __rt_lib_shutdown                        0x00000091   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_stdio_1                0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fp_trap_1              0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_signal_1               0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_user_alloc_1           0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_heap_1                 0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x00000093   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __cxa_finalize                            - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __scatterload                            0x00000001   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x00000001   Thumb Code    74  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x00000001   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x0000000b   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x00000001   Thumb Code     2  __scatter.o(!!handler_null)
    Region$$Table$$Base                       - Undefined Reference
    Region$$Table$$Limit                      - Undefined Reference
    __scatterload_copy                       0x00000001   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x00000001   Thumb Code    28  __scatter_zi.o(!!handler_zi)


