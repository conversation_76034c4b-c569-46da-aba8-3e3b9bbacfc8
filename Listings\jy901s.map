Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to hw_jy901s.o(.text.jy901s_init) for jy901s_init
    empty.o(.text.main) refers to hw_jy901s.o(.text.get_angle) for get_angle
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_start) for i2c_start
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_send_byte) for i2c_send_byte
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_wait_ack) for i2c_wait_ack
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.ARM.exidx.text.jy901s_write_reg) refers to hw_jy901s.o(.text.jy901s_write_reg) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_start) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_start) refers to hw_jy901s.o(.text.i2c_start) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_send_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_send_byte) refers to hw_jy901s.o(.text.i2c_send_byte) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.ARM.exidx.text.i2c_wait_ack) refers to hw_jy901s.o(.text.i2c_wait_ack) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_stop) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_stop) refers to hw_jy901s.o(.text.i2c_stop) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_start) for i2c_start
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_send_byte) for i2c_send_byte
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_wait_ack) for i2c_wait_ack
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.text.jy901s_read_data) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_read_byte) for i2c_read_byte
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_send_ack) for i2c_send_ack
    hw_jy901s.o(.ARM.exidx.text.jy901s_read_data) refers to hw_jy901s.o(.text.jy901s_read_data) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_read_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_jy901s.o(.ARM.exidx.text.i2c_read_byte) refers to hw_jy901s.o(.text.i2c_read_byte) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_send_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_send_ack) refers to hw_jy901s.o(.text.i2c_send_ack) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_init) refers to hw_jy901s.o(.text.jy901s_write_reg) for jy901s_write_reg
    hw_jy901s.o(.text.jy901s_init) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.jy901s_init) refers to hw_jy901s.o(.text.jy901s_init) for [Anonymous Symbol]
    hw_jy901s.o(.text.get_angle) refers to hw_jy901s.o(.text.jy901s_read_data) for jy901s_read_data
    hw_jy901s.o(.text.get_angle) refers to dflti.o(.text) for __aeabi_i2d
    hw_jy901s.o(.text.get_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    hw_jy901s.o(.text.get_angle) refers to dmul.o(.text) for __aeabi_dmul
    hw_jy901s.o(.text.get_angle) refers to d2f.o(.text) for __aeabi_d2f
    hw_jy901s.o(.text.get_angle) refers to f2d.o(.text) for __aeabi_f2d
    hw_jy901s.o(.text.get_angle) refers to dcmple.o(.text) for __aeabi_dcmple
    hw_jy901s.o(.text.get_angle) refers to dadd.o(.text) for __aeabi_dadd
    hw_jy901s.o(.text.get_angle) refers to dcmpge.o(.text) for __aeabi_dcmpge
    hw_jy901s.o(.text.get_angle) refers to hw_jy901s.o(.bss.angle_struct) for angle_struct
    hw_jy901s.o(.ARM.exidx.text.get_angle) refers to hw_jy901s.o(.text.get_angle) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.delay_cycles) refers to ti_msp_dl_config.o(.text.delay_cycles) for [Anonymous Symbol]
    system_mspm0g3507.o(.text.SystemCoreClockUpdate) refers to system_mspm0g3507.o(.data.SystemCoreClock) for SystemCoreClock
    system_mspm0g3507.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_mspm0g3507.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_mspm0g3507.o(.text.SystemInit) refers to system_mspm0g3507.o(.data.SystemCoreClock) for SystemCoreClock
    system_mspm0g3507.o(.ARM.exidx.text.SystemInit) refers to system_mspm0g3507.o(.text.SystemInit) for [Anonymous Symbol]
    startup_mspm0g3507.o(RESET) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    startup_mspm0g3507.o(RESET) refers to startup_mspm0g3507.o(.text) for Reset_Handler
    startup_mspm0g3507.o(.text) refers to system_mspm0g3507.o(.text.SystemInit) for SystemInit
    startup_mspm0g3507.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing hw_jy901s.o(.text), (0 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_write_reg), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_start), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_send_byte), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_wait_ack), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_stop), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_read_data), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_read_byte), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_send_ack), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_init), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.get_angle), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing hw_jy901s.o(.rodata..L__const.jy901s_init.unlock_reg1), (2 bytes).
    Removing hw_jy901s.o(.rodata.str1.1), (4 bytes).
    Removing hw_jy901s.o(.rodata..L__const.jy901s_init.unlock_reg), (2 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.delay_cycles), (8 bytes).
    Removing system_mspm0g3507.o(.text), (0 bytes).
    Removing system_mspm0g3507.o(.text.SystemCoreClockUpdate), (16 bytes).
    Removing system_mspm0g3507.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_mspm0g3507.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing startup_mspm0g3507.o(HEAP), (1024 bytes).

36 unused section(s) (total 1260 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpge.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hw_jy901s.c                              0x00000000   Number         0  hw_jy901s.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_mspm0g3507.s                     0x00000000   Number         0  startup_mspm0g3507.o ABSOLUTE
    system_mspm0g3507.c                      0x00000000   Number         0  system_mspm0g3507.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      132  startup_mspm0g3507.o(RESET)
    .ARM.Collect$$$$00000000                 0x00000084   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00000084   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00000088   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x0000008c   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x0000008c   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x0000008c   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x00000094   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x00000094   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x00000094   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x00000094   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x00000098   Section       28  startup_mspm0g3507.o(.text)
    .text                                    0x000000b4   Section        0  dadd.o(.text)
    .text                                    0x00000218   Section        0  dmul.o(.text)
    .text                                    0x000002e8   Section        0  ddiv.o(.text)
    .text                                    0x000003d8   Section        0  dcmple.o(.text)
    .text                                    0x00000404   Section        0  dcmpge.o(.text)
    .text                                    0x00000430   Section        0  dflti.o(.text)
    .text                                    0x00000458   Section        0  f2d.o(.text)
    .text                                    0x00000480   Section        0  d2f.o(.text)
    .text                                    0x000004b8   Section        0  llshl.o(.text)
    .text                                    0x000004d8   Section        0  llsshr.o(.text)
    .text                                    0x000004fe   Section        0  iusefp.o(.text)
    .text                                    0x000004fe   Section        0  fepilogue.o(.text)
    .text                                    0x00000580   Section        0  depilogue.o(.text)
    .text                                    0x00000640   Section       48  init.o(.text)
    .text                                    0x00000670   Section        0  llushr.o(.text)
    DL_GPIO_clearPins                        0x00000693   Thumb Code    16  hw_jy901s.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000692   Section        0  hw_jy901s.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableOutput                     0x000006a3   Thumb Code    20  hw_jy901s.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x000006a2   Section        0  hw_jy901s.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x000006b7   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x000006b6   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_initDigitalInput                 0x000006bf   Thumb Code     8  hw_jy901s.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x000006be   Section        0  hw_jy901s.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalOutput                0x000006c7   Thumb Code     8  hw_jy901s.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x000006c6   Section        0  hw_jy901s.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_readPins                         0x000006cf   Thumb Code    18  hw_jy901s.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x000006ce   Section        0  hw_jy901s.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x000006e1   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x000006e0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setPins                          0x000006e9   Thumb Code    16  hw_jy901s.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x000006e8   Section        0  hw_jy901s.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x000006f8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000006fa   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000006fc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x0000070c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00000738   Section        0  system_mspm0g3507.o(.text.SystemInit)
    __arm_cp.1_0                             0x00000740   Number         4  system_mspm0g3507.o(.text.SystemInit)
    __arm_cp.1_1                             0x00000744   Number         4  system_mspm0g3507.o(.text.SystemInit)
    [Anonymous Symbol]                       0x00000748   Section        0  ti_msp_dl_config.o(.text.delay_cycles)
    [Anonymous Symbol]                       0x00000764   Section        0  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_0                             0x00000920   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_1                             0x00000924   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_2                             0x00000928   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_3                             0x0000092c   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_4                             0x00000930   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_5                             0x00000934   Number         4  hw_jy901s.o(.text.get_angle)
    i2c_read_byte                            0x00000939   Thumb Code   164  hw_jy901s.o(.text.i2c_read_byte)
    [Anonymous Symbol]                       0x00000938   Section        0  hw_jy901s.o(.text.i2c_read_byte)
    i2c_send_ack                             0x000009dd   Thumb Code   144  hw_jy901s.o(.text.i2c_send_ack)
    [Anonymous Symbol]                       0x000009dc   Section        0  hw_jy901s.o(.text.i2c_send_ack)
    __arm_cp.7_0                             0x00000a6c   Number         4  hw_jy901s.o(.text.i2c_send_ack)
    i2c_send_byte                            0x00000a71   Thumb Code   168  hw_jy901s.o(.text.i2c_send_byte)
    [Anonymous Symbol]                       0x00000a70   Section        0  hw_jy901s.o(.text.i2c_send_byte)
    i2c_start                                0x00000b19   Thumb Code   104  hw_jy901s.o(.text.i2c_start)
    [Anonymous Symbol]                       0x00000b18   Section        0  hw_jy901s.o(.text.i2c_start)
    i2c_stop                                 0x00000b81   Thumb Code    92  hw_jy901s.o(.text.i2c_stop)
    [Anonymous Symbol]                       0x00000b80   Section        0  hw_jy901s.o(.text.i2c_stop)
    i2c_wait_ack                             0x00000bdd   Thumb Code   200  hw_jy901s.o(.text.i2c_wait_ack)
    [Anonymous Symbol]                       0x00000bdc   Section        0  hw_jy901s.o(.text.i2c_wait_ack)
    __arm_cp.3_0                             0x00000ca4   Number         4  hw_jy901s.o(.text.i2c_wait_ack)
    [Anonymous Symbol]                       0x00000ca8   Section        0  hw_jy901s.o(.text.jy901s_init)
    __arm_cp.8_0                             0x00000d3c   Number         4  hw_jy901s.o(.text.jy901s_init)
    __arm_cp.8_1                             0x00000d40   Number         4  hw_jy901s.o(.text.jy901s_init)
    [Anonymous Symbol]                       0x00000d44   Section        0  hw_jy901s.o(.text.jy901s_read_data)
    [Anonymous Symbol]                       0x00000e20   Section        0  hw_jy901s.o(.text.jy901s_write_reg)
    [Anonymous Symbol]                       0x00000ed4   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00000efc   Number         4  empty.o(.text.main)
    i.__ARM_clz                              0x00000f00   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00000f2e   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00000f3c   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00000f3e   Section       14  handlers.o(i.__scatterload_zeroinit)
    STACK                                    0x20000018   Section     1024  startup_mspm0g3507.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g3507.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x00000084   Data           0  startup_mspm0g3507.o(RESET)
    __Vectors_Size                           0x00000084   Number         0  startup_mspm0g3507.o ABSOLUTE
    __main                                   0x00000085   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00000085   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00000089   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x0000008d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x0000008d   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x0000008d   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x0000008d   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x00000095   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x00000095   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x00000099   Thumb Code     8  startup_mspm0g3507.o(.text)
    NMI_Handler                              0x000000a1   Thumb Code     2  startup_mspm0g3507.o(.text)
    HardFault_Handler                        0x000000a3   Thumb Code     2  startup_mspm0g3507.o(.text)
    SVC_Handler                              0x000000a5   Thumb Code     2  startup_mspm0g3507.o(.text)
    PendSV_Handler                           0x000000a7   Thumb Code     2  startup_mspm0g3507.o(.text)
    SysTick_Handler                          0x000000a9   Thumb Code     2  startup_mspm0g3507.o(.text)
    ADC0_IRQHandler                          0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    ADC1_IRQHandler                          0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    CANFD_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    DAC_IRQHandler                           0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    GROUP0_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    GROUP1_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    I2C_INST_IRQHandler                      0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    SPI_INST_IRQHandler                      0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMA0_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMA1_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG0_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG12_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG14_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG6_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG7_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG8_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    UART_INST_IRQHandler                     0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    __aeabi_dadd                             0x000000b5   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x000001fd   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00000209   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000219   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000002e9   Thumb Code   234  ddiv.o(.text)
    __aeabi_dcmple                           0x000003d9   Thumb Code    44  dcmple.o(.text)
    __aeabi_dcmpge                           0x00000405   Thumb Code    44  dcmpge.o(.text)
    __aeabi_i2d                              0x00000431   Thumb Code    34  dflti.o(.text)
    __aeabi_f2d                              0x00000459   Thumb Code    40  f2d.o(.text)
    __aeabi_d2f                              0x00000481   Thumb Code    56  d2f.o(.text)
    __aeabi_llsl                             0x000004b9   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000004b9   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x000004d9   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x000004d9   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x000004ff   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x000004ff   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x0000050f   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x00000581   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x0000059b   Thumb Code   164  depilogue.o(.text)
    __scatterload                            0x00000641   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000641   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x00000671   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x00000671   Thumb Code     0  llushr.o(.text)
    SYSCFG_DL_GPIO_init                      0x000006f9   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_SYSCTL_init                    0x000006fb   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_init                           0x000006fd   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x0000070d   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SystemInit                               0x00000739   Thumb Code     8  system_mspm0g3507.o(.text.SystemInit)
    delay_cycles                             0x00000749   Thumb Code    26  ti_msp_dl_config.o(.text.delay_cycles)
    get_angle                                0x00000765   Thumb Code   444  hw_jy901s.o(.text.get_angle)
    jy901s_init                              0x00000ca9   Thumb Code   148  hw_jy901s.o(.text.jy901s_init)
    jy901s_read_data                         0x00000d45   Thumb Code   220  hw_jy901s.o(.text.jy901s_read_data)
    jy901s_write_reg                         0x00000e21   Thumb Code   180  hw_jy901s.o(.text.jy901s_write_reg)
    main                                     0x00000ed5   Thumb Code    40  empty.o(.text.main)
    __ARM_clz                                0x00000f01   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00000f2f   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00000f3d   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00000f3f   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x00000f4c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00000f6c   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_mspm0g3507.o(.data.SystemCoreClock)
    angle_struct                             0x20000008   Data          12  hw_jy901s.o(.bss.angle_struct)
    __initial_sp                             0x20000418   Data           0  startup_mspm0g3507.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00000099

  Load Region LR_1 (Base: 0x00000000, Size: 0x00000f70, Max: 0xffffffff, ABSOLUTE)

    Execution Region ER_RO (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00000f6c, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x00000084   Data   RO           86    RESET               startup_mspm0g3507.o
    0x00000084   0x00000084   0x00000000   Code   RO           95  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x00000084   0x00000084   0x00000004   Code   RO          114    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x00000088   0x00000088   0x00000004   Code   RO          117    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x0000008c   0x0000008c   0x00000000   Code   RO          119    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x0000008c   0x0000008c   0x00000000   Code   RO          121    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x0000008c   0x0000008c   0x00000008   Code   RO          122    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x00000094   0x00000094   0x00000000   Code   RO          124    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x00000094   0x00000094   0x00000000   Code   RO          126    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x00000094   0x00000094   0x00000004   Code   RO          115    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x00000098   0x00000098   0x0000001c   Code   RO           87  * .text               startup_mspm0g3507.o
    0x000000b4   0x000000b4   0x00000164   Code   RO           98    .text               mf_p.l(dadd.o)
    0x00000218   0x00000218   0x000000d0   Code   RO          100    .text               mf_p.l(dmul.o)
    0x000002e8   0x000002e8   0x000000f0   Code   RO          102    .text               mf_p.l(ddiv.o)
    0x000003d8   0x000003d8   0x0000002c   Code   RO          104    .text               mf_p.l(dcmple.o)
    0x00000404   0x00000404   0x0000002c   Code   RO          106    .text               mf_p.l(dcmpge.o)
    0x00000430   0x00000430   0x00000028   Code   RO          108    .text               mf_p.l(dflti.o)
    0x00000458   0x00000458   0x00000028   Code   RO          110    .text               mf_p.l(f2d.o)
    0x00000480   0x00000480   0x00000038   Code   RO          112    .text               mf_p.l(d2f.o)
    0x000004b8   0x000004b8   0x00000020   Code   RO          128    .text               mc_p.l(llshl.o)
    0x000004d8   0x000004d8   0x00000026   Code   RO          130    .text               mc_p.l(llsshr.o)
    0x000004fe   0x000004fe   0x00000000   Code   RO          132    .text               mc_p.l(iusefp.o)
    0x000004fe   0x000004fe   0x00000082   Code   RO          133    .text               mf_p.l(fepilogue.o)
    0x00000580   0x00000580   0x000000be   Code   RO          135    .text               mf_p.l(depilogue.o)
    0x0000063e   0x0000063e   0x00000002   PAD
    0x00000640   0x00000640   0x00000030   Code   RO          139    .text               mc_p.l(init.o)
    0x00000670   0x00000670   0x00000022   Code   RO          141    .text               mc_p.l(llushr.o)
    0x00000692   0x00000692   0x00000010   Code   RO           37    .text.DL_GPIO_clearPins  hw_jy901s.o
    0x000006a2   0x000006a2   0x00000014   Code   RO           35    .text.DL_GPIO_enableOutput  hw_jy901s.o
    0x000006b6   0x000006b6   0x00000008   Code   RO           64    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x000006be   0x000006be   0x00000008   Code   RO           39    .text.DL_GPIO_initDigitalInput  hw_jy901s.o
    0x000006c6   0x000006c6   0x00000008   Code   RO           31    .text.DL_GPIO_initDigitalOutput  hw_jy901s.o
    0x000006ce   0x000006ce   0x00000012   Code   RO           41    .text.DL_GPIO_readPins  hw_jy901s.o
    0x000006e0   0x000006e0   0x00000008   Code   RO           62    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x000006e8   0x000006e8   0x00000010   Code   RO           33    .text.DL_GPIO_setPins  hw_jy901s.o
    0x000006f8   0x000006f8   0x00000002   Code   RO           60    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000006fa   0x000006fa   0x00000002   Code   RO           58    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x000006fc   0x000006fc   0x00000010   Code   RO           54    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x0000070c   0x0000070c   0x0000002c   Code   RO           56    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00000738   0x00000738   0x00000010   Code   RO           77    .text.SystemInit    system_mspm0g3507.o
    0x00000748   0x00000748   0x0000001a   Code   RO           66    .text.delay_cycles  ti_msp_dl_config.o
    0x00000762   0x00000762   0x00000002   PAD
    0x00000764   0x00000764   0x000001d4   Code   RO           29    .text.get_angle     hw_jy901s.o
    0x00000938   0x00000938   0x000000a4   Code   RO           23    .text.i2c_read_byte  hw_jy901s.o
    0x000009dc   0x000009dc   0x00000094   Code   RO           25    .text.i2c_send_ack  hw_jy901s.o
    0x00000a70   0x00000a70   0x000000a8   Code   RO           15    .text.i2c_send_byte  hw_jy901s.o
    0x00000b18   0x00000b18   0x00000068   Code   RO           13    .text.i2c_start     hw_jy901s.o
    0x00000b80   0x00000b80   0x0000005c   Code   RO           19    .text.i2c_stop      hw_jy901s.o
    0x00000bdc   0x00000bdc   0x000000cc   Code   RO           17    .text.i2c_wait_ack  hw_jy901s.o
    0x00000ca8   0x00000ca8   0x0000009c   Code   RO           27    .text.jy901s_init   hw_jy901s.o
    0x00000d44   0x00000d44   0x000000dc   Code   RO           21    .text.jy901s_read_data  hw_jy901s.o
    0x00000e20   0x00000e20   0x000000b4   Code   RO           11    .text.jy901s_write_reg  hw_jy901s.o
    0x00000ed4   0x00000ed4   0x0000002c   Code   RO            2    .text.main          empty.o
    0x00000f00   0x00000f00   0x0000002e   Code   RO          137    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00000f2e   0x00000f2e   0x0000000e   Code   RO          145    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00000f3c   0x00000f3c   0x00000002   Code   RO          146    i.__scatterload_null  mc_p.l(handlers.o)
    0x00000f3e   0x00000f3e   0x0000000e   Code   RO          147    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x00000f4c   0x00000f4c   0x00000020   Data   RO          144    Region$$Table       anon$$obj.o


    Execution Region ER_RW (Exec base: 0x20000000, Load base: 0x00000f6c, Size: 0x00000004, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000f6c   0x00000004   Data   RW           79    .data.SystemCoreClock  system_mspm0g3507.o


    Execution Region ER_ZI (Exec base: 0x20000008, Load base: 0x00000f70, Size: 0x00000410, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000008        -       0x0000000c   Zero   RW           46    .bss.angle_struct   hw_jy901s.o
    0x20000014   0x00000f70   0x00000004   PAD
    0x20000018        -       0x00000400   Zero   RW           88    STACK               startup_mspm0g3507.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        44          4          0          0          0        596   empty.o
      1990         40          0          0         12       4337   hw_jy901s.o
        28          8        132          0       1024        644   startup_mspm0g3507.o
        16          8          0          4          0        660   system_mspm0g3507.o
       106          0          0          0          0       1344   ti_msp_dl_config.o

    ----------------------------------------------------------------------
      2186         <USER>        <GROUP>          4       1040       7581   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
        44          0          0          0          0         68   dcmpge.o
        44          0          0          0          0         68   dcmple.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        40          6          0          0          0         68   dflti.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       130          0          0          0          0        144   fepilogue.o

    ----------------------------------------------------------------------
      1598         <USER>          <GROUP>          0          0       1276   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       202         18          0          0          0        272   mc_p.l
      1394         22          0          0          0       1004   mf_p.l

    ----------------------------------------------------------------------
      1598         <USER>          <GROUP>          0          0       1276   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      3784        100        164          4       1040       8377   Grand Totals
      3784        100        164          4       1040       8377   ELF Image Totals
      3784        100        164          4          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 3948 (   3.86kB)
    Total RW  Size (RW Data + ZI Data)              1044 (   1.02kB)
    Total ROM Size (Code + RO Data + RW Data)       3952 (   3.86kB)

==============================================================================

