Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    empty.o(.text.main) refers to hw_jy901s.o(.text.jy901s_init) for jy901s_init
    empty.o(.text.main) refers to hw_ssd1306.o(.text.oled_init) for oled_init
    empty.o(.text.main) refers to hw_jy901s.o(.text.get_angle) for get_angle
    empty.o(.text.main) refers to hw_ssd1306.o(.text.oled_display_attitude) for oled_display_attitude
    empty.o(.text.main) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    empty.o(.ARM.exidx.text.main) refers to empty.o(.text.main) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_start) for i2c_start
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_send_byte) for i2c_send_byte
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_wait_ack) for i2c_wait_ack
    hw_jy901s.o(.text.jy901s_write_reg) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.ARM.exidx.text.jy901s_write_reg) refers to hw_jy901s.o(.text.jy901s_write_reg) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_start) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_start) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_start) refers to hw_jy901s.o(.text.i2c_start) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_send_byte) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_send_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_send_byte) refers to hw_jy901s.o(.text.i2c_send_byte) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_wait_ack) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.ARM.exidx.text.i2c_wait_ack) refers to hw_jy901s.o(.text.i2c_wait_ack) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_stop) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_stop) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_stop) refers to hw_jy901s.o(.text.i2c_stop) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_start) for i2c_start
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_send_byte) for i2c_send_byte
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_wait_ack) for i2c_wait_ack
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_stop) for i2c_stop
    hw_jy901s.o(.text.jy901s_read_data) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_read_byte) for i2c_read_byte
    hw_jy901s.o(.text.jy901s_read_data) refers to hw_jy901s.o(.text.i2c_send_ack) for i2c_send_ack
    hw_jy901s.o(.ARM.exidx.text.jy901s_read_data) refers to hw_jy901s.o(.text.jy901s_read_data) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_read_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_read_byte) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_jy901s.o(.ARM.exidx.text.i2c_read_byte) refers to hw_jy901s.o(.text.i2c_read_byte) for [Anonymous Symbol]
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_jy901s.o(.text.i2c_send_ack) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_jy901s.o(.text.i2c_send_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.i2c_send_ack) refers to hw_jy901s.o(.text.i2c_send_ack) for [Anonymous Symbol]
    hw_jy901s.o(.text.jy901s_init) refers to hw_jy901s.o(.text.jy901s_write_reg) for jy901s_write_reg
    hw_jy901s.o(.text.jy901s_init) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_jy901s.o(.ARM.exidx.text.jy901s_init) refers to hw_jy901s.o(.text.jy901s_init) for [Anonymous Symbol]
    hw_jy901s.o(.text.get_angle) refers to hw_jy901s.o(.text.jy901s_read_data) for jy901s_read_data
    hw_jy901s.o(.text.get_angle) refers to dflti.o(.text) for __aeabi_i2d
    hw_jy901s.o(.text.get_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    hw_jy901s.o(.text.get_angle) refers to dmul.o(.text) for __aeabi_dmul
    hw_jy901s.o(.text.get_angle) refers to d2f.o(.text) for __aeabi_d2f
    hw_jy901s.o(.text.get_angle) refers to f2d.o(.text) for __aeabi_f2d
    hw_jy901s.o(.text.get_angle) refers to dcmple.o(.text) for __aeabi_dcmple
    hw_jy901s.o(.text.get_angle) refers to dadd.o(.text) for __aeabi_dadd
    hw_jy901s.o(.text.get_angle) refers to dcmpge.o(.text) for __aeabi_dcmpge
    hw_jy901s.o(.text.get_angle) refers to hw_jy901s.o(.bss.angle_struct) for angle_struct
    hw_jy901s.o(.ARM.exidx.text.get_angle) refers to hw_jy901s.o(.text.get_angle) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_jy901s.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to hw_jy901s.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_jy901s.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to hw_jy901s.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    hw_jy901s.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_jy901s.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_start) for oled_i2c_start
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_send_byte) for oled_i2c_send_byte
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_wait_ack) for oled_i2c_wait_ack
    hw_ssd1306.o(.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_i2c_stop) for oled_i2c_stop
    hw_ssd1306.o(.ARM.exidx.text.oled_write_cmd) refers to hw_ssd1306.o(.text.oled_write_cmd) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_ssd1306.o(.text.oled_i2c_start) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.text.oled_i2c_start) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_start) refers to hw_ssd1306.o(.text.oled_i2c_start) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.text.oled_i2c_send_byte) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_send_byte) refers to hw_ssd1306.o(.text.oled_i2c_send_byte) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalInput) for DL_GPIO_initDigitalInput
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.oled_i2c_stop) for oled_i2c_stop
    hw_ssd1306.o(.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_wait_ack) refers to hw_ssd1306.o(.text.oled_i2c_wait_ack) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    hw_ssd1306.o(.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    hw_ssd1306.o(.text.oled_i2c_stop) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.ARM.exidx.text.oled_i2c_stop) refers to hw_ssd1306.o(.text.oled_i2c_stop) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_start) for oled_i2c_start
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_send_byte) for oled_i2c_send_byte
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_wait_ack) for oled_i2c_wait_ack
    hw_ssd1306.o(.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_i2c_stop) for oled_i2c_stop
    hw_ssd1306.o(.ARM.exidx.text.oled_write_data) refers to hw_ssd1306.o(.text.oled_write_data) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_init) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_write_cmd) for oled_write_cmd
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_clear) for oled_clear
    hw_ssd1306.o(.text.oled_init) refers to hw_ssd1306.o(.text.oled_update_display) for oled_update_display
    hw_ssd1306.o(.ARM.exidx.text.oled_init) refers to hw_ssd1306.o(.text.oled_init) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_clear) refers to memseta.o(.text) for __aeabi_memclr
    hw_ssd1306.o(.text.oled_clear) refers to hw_ssd1306.o(.bss.oled_buffer) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_clear) refers to hw_ssd1306.o(.text.oled_clear) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_update_display) refers to hw_ssd1306.o(.text.oled_write_cmd) for oled_write_cmd
    hw_ssd1306.o(.text.oled_update_display) refers to hw_ssd1306.o(.text.oled_write_data) for oled_write_data
    hw_ssd1306.o(.text.oled_update_display) refers to hw_ssd1306.o(.bss.oled_buffer) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_update_display) refers to hw_ssd1306.o(.text.oled_update_display) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_set_pixel) refers to hw_ssd1306.o(.bss.oled_buffer) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_set_pixel) refers to hw_ssd1306.o(.text.oled_set_pixel) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_draw_char) refers to hw_ssd1306.o(.text.oled_set_pixel) for oled_set_pixel
    hw_ssd1306.o(.text.oled_draw_char) refers to hw_ssd1306.o(.rodata.font8x16) for font8x16
    hw_ssd1306.o(.ARM.exidx.text.oled_draw_char) refers to hw_ssd1306.o(.text.oled_draw_char) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_draw_string) refers to hw_ssd1306.o(.text.oled_draw_char) for oled_draw_char
    hw_ssd1306.o(.ARM.exidx.text.oled_draw_string) refers to hw_ssd1306.o(.text.oled_draw_string) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_draw_number) refers to fcmpge.o(.text) for __aeabi_fcmpge
    hw_ssd1306.o(.text.oled_draw_number) refers to ffixi.o(.text) for __aeabi_f2iz
    hw_ssd1306.o(.text.oled_draw_number) refers to idiv_div0.o(.text) for __aeabi_idivmod
    hw_ssd1306.o(.text.oled_draw_number) refers to fflti.o(.text) for __aeabi_i2f
    hw_ssd1306.o(.text.oled_draw_number) refers to fadd.o(.text) for __aeabi_fsub
    hw_ssd1306.o(.text.oled_draw_number) refers to fmul.o(.text) for __aeabi_fmul
    hw_ssd1306.o(.text.oled_draw_number) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    hw_ssd1306.o(.text.oled_draw_number) refers to hw_ssd1306.o(.text.oled_draw_string) for oled_draw_string
    hw_ssd1306.o(.ARM.exidx.text.oled_draw_number) refers to hw_ssd1306.o(.text.oled_draw_number) for [Anonymous Symbol]
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_clear) for oled_clear
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_draw_string) for oled_draw_string
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_draw_number) for oled_draw_number
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_update_display) for oled_update_display
    hw_ssd1306.o(.text.oled_display_attitude) refers to hw_ssd1306.o(.rodata.str1.1) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.oled_display_attitude) refers to hw_ssd1306.o(.text.oled_display_attitude) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_setPins) refers to hw_ssd1306.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to hw_ssd1306.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to hw_ssd1306.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalInput) refers to hw_ssd1306.o(.text.DL_GPIO_initDigitalInput) for [Anonymous Symbol]
    hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_readPins) refers to hw_ssd1306.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.delay_cycles) for delay_cycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.delay_cycles) refers to ti_msp_dl_config.o(.text.delay_cycles) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    system_mspm0g3507.o(.text.SystemCoreClockUpdate) refers to system_mspm0g3507.o(.data.SystemCoreClock) for SystemCoreClock
    system_mspm0g3507.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_mspm0g3507.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_mspm0g3507.o(.text.SystemInit) refers to system_mspm0g3507.o(.data.SystemCoreClock) for SystemCoreClock
    system_mspm0g3507.o(.ARM.exidx.text.SystemInit) refers to system_mspm0g3507.o(.text.SystemInit) for [Anonymous Symbol]
    startup_mspm0g3507.o(RESET) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    startup_mspm0g3507.o(RESET) refers to startup_mspm0g3507.o(.text) for Reset_Handler
    startup_mspm0g3507.o(.text) refers to system_mspm0g3507.o(.text.SystemInit) for SystemInit
    startup_mspm0g3507.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    idiv.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g3507.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to empty.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to empty.o(.text.main) for main
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing empty.o(.text), (0 bytes).
    Removing empty.o(.ARM.exidx.text.main), (8 bytes).
    Removing empty.o(.ARM.use_no_argv), (4 bytes).
    Removing hw_jy901s.o(.text), (0 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_write_reg), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_start), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_send_byte), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_wait_ack), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_stop), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_read_data), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_read_byte), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.i2c_send_ack), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.jy901s_init), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.get_angle), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing hw_jy901s.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing hw_jy901s.o(.rodata..L__const.jy901s_init.unlock_reg1), (2 bytes).
    Removing hw_jy901s.o(.rodata.str1.1), (4 bytes).
    Removing hw_jy901s.o(.rodata..L__const.jy901s_init.unlock_reg), (2 bytes).
    Removing hw_ssd1306.o(.text), (0 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_write_cmd), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_start), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_send_byte), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_wait_ack), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_i2c_stop), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_write_data), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_init), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_clear), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_update_display), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_set_pixel), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_draw_char), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_draw_string), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_draw_number), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.oled_display_attitude), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_initDigitalInput), (8 bytes).
    Removing hw_ssd1306.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.delay_cycles), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing system_mspm0g3507.o(.text), (0 bytes).
    Removing system_mspm0g3507.o(.text.SystemCoreClockUpdate), (16 bytes).
    Removing system_mspm0g3507.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_mspm0g3507.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing startup_mspm0g3507.o(HEAP), (1024 bytes).

60 unused section(s) (total 1444 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  dcmpge.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    empty.c                                  0x00000000   Number         0  empty.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hw_jy901s.c                              0x00000000   Number         0  hw_jy901s.o ABSOLUTE
    hw_ssd1306.c                             0x00000000   Number         0  hw_ssd1306.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_mspm0g3507.s                     0x00000000   Number         0  startup_mspm0g3507.o ABSOLUTE
    system_mspm0g3507.c                      0x00000000   Number         0  system_mspm0g3507.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    RESET                                    0x00000000   Section      132  startup_mspm0g3507.o(RESET)
    .ARM.Collect$$$$00000000                 0x00000084   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00000084   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00000088   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x0000008c   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x0000008c   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x0000008c   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x00000094   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x00000094   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x00000094   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x00000094   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x00000098   Section       28  startup_mspm0g3507.o(.text)
    .text                                    0x000000b4   Section        0  memseta.o(.text)
    .text                                    0x000000d8   Section        0  fadd.o(.text)
    .text                                    0x0000018a   Section        0  fmul.o(.text)
    .text                                    0x00000204   Section        0  dadd.o(.text)
    .text                                    0x00000368   Section        0  dmul.o(.text)
    .text                                    0x00000438   Section        0  ddiv.o(.text)
    .text                                    0x00000528   Section        0  fcmpge.o(.text)
    .text                                    0x00000544   Section        0  dcmple.o(.text)
    .text                                    0x00000570   Section        0  dcmpge.o(.text)
    .text                                    0x0000059c   Section        0  fflti.o(.text)
    .text                                    0x000005b4   Section        0  dflti.o(.text)
    .text                                    0x000005dc   Section        0  ffixi.o(.text)
    .text                                    0x0000060e   Section        0  f2d.o(.text)
    .text                                    0x00000636   Section        0  d2f.o(.text)
    .text                                    0x0000066e   Section        0  uidiv_div0.o(.text)
    .text                                    0x000006ac   Section        0  idiv_div0.o(.text)
    .text                                    0x000006fc   Section        0  llshl.o(.text)
    .text                                    0x0000071c   Section        0  llsshr.o(.text)
    .text                                    0x00000742   Section        0  iusefp.o(.text)
    .text                                    0x00000742   Section        0  fepilogue.o(.text)
    .text                                    0x000007c4   Section        0  depilogue.o(.text)
    .text                                    0x00000884   Section       48  init.o(.text)
    .text                                    0x000008b4   Section        0  llushr.o(.text)
    DL_GPIO_clearPins                        0x000008d7   Thumb Code    16  hw_jy901s.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000008d6   Section        0  hw_jy901s.o(.text.DL_GPIO_clearPins)
    DL_GPIO_clearPins                        0x000008e7   Thumb Code    16  hw_ssd1306.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x000008e6   Section        0  hw_ssd1306.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableOutput                     0x000008f7   Thumb Code    20  hw_jy901s.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x000008f6   Section        0  hw_jy901s.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enableOutput                     0x0000090b   Thumb Code    20  hw_ssd1306.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x0000090a   Section        0  hw_ssd1306.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enableOutput                     0x0000091f   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x0000091e   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x00000933   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x00000932   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_initDigitalInput                 0x0000093b   Thumb Code     8  hw_jy901s.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x0000093a   Section        0  hw_jy901s.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalInput                 0x00000943   Thumb Code     8  hw_ssd1306.o(.text.DL_GPIO_initDigitalInput)
    [Anonymous Symbol]                       0x00000942   Section        0  hw_ssd1306.o(.text.DL_GPIO_initDigitalInput)
    DL_GPIO_initDigitalOutput                0x0000094b   Thumb Code     8  hw_jy901s.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x0000094a   Section        0  hw_jy901s.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutput                0x00000953   Thumb Code     8  hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000952   Section        0  hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutput                0x0000095b   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x0000095a   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_readPins                         0x00000963   Thumb Code    18  hw_jy901s.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000962   Section        0  hw_jy901s.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x00000975   Thumb Code    18  hw_ssd1306.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000974   Section        0  hw_ssd1306.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000987   Thumb Code     8  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000986   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setPins                          0x0000098f   Thumb Code    16  hw_jy901s.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000098e   Section        0  hw_jy901s.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x0000099f   Thumb Code    16  hw_ssd1306.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x0000099e   Section        0  hw_ssd1306.o(.text.DL_GPIO_setPins)
    DL_GPIO_setPins                          0x000009af   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x000009ae   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setPins)
    [Anonymous Symbol]                       0x000009c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x00000a38   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x00000a3a   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00000a4c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    [Anonymous Symbol]                       0x00000a78   Section        0  system_mspm0g3507.o(.text.SystemInit)
    __arm_cp.1_0                             0x00000a80   Number         4  system_mspm0g3507.o(.text.SystemInit)
    __arm_cp.1_1                             0x00000a84   Number         4  system_mspm0g3507.o(.text.SystemInit)
    [Anonymous Symbol]                       0x00000a88   Section        0  ti_msp_dl_config.o(.text.delay_cycles)
    [Anonymous Symbol]                       0x00000aa4   Section        0  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_0                             0x00000c70   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_1                             0x00000c74   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_2                             0x00000c78   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_3                             0x00000c7c   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_4                             0x00000c80   Number         4  hw_jy901s.o(.text.get_angle)
    __arm_cp.9_5                             0x00000c84   Number         4  hw_jy901s.o(.text.get_angle)
    i2c_read_byte                            0x00000c89   Thumb Code   164  hw_jy901s.o(.text.i2c_read_byte)
    [Anonymous Symbol]                       0x00000c88   Section        0  hw_jy901s.o(.text.i2c_read_byte)
    i2c_send_ack                             0x00000d2d   Thumb Code   144  hw_jy901s.o(.text.i2c_send_ack)
    [Anonymous Symbol]                       0x00000d2c   Section        0  hw_jy901s.o(.text.i2c_send_ack)
    __arm_cp.7_0                             0x00000dbc   Number         4  hw_jy901s.o(.text.i2c_send_ack)
    i2c_send_byte                            0x00000dc1   Thumb Code   168  hw_jy901s.o(.text.i2c_send_byte)
    [Anonymous Symbol]                       0x00000dc0   Section        0  hw_jy901s.o(.text.i2c_send_byte)
    i2c_start                                0x00000e69   Thumb Code   104  hw_jy901s.o(.text.i2c_start)
    [Anonymous Symbol]                       0x00000e68   Section        0  hw_jy901s.o(.text.i2c_start)
    i2c_stop                                 0x00000ed1   Thumb Code    92  hw_jy901s.o(.text.i2c_stop)
    [Anonymous Symbol]                       0x00000ed0   Section        0  hw_jy901s.o(.text.i2c_stop)
    i2c_wait_ack                             0x00000f2d   Thumb Code   200  hw_jy901s.o(.text.i2c_wait_ack)
    [Anonymous Symbol]                       0x00000f2c   Section        0  hw_jy901s.o(.text.i2c_wait_ack)
    __arm_cp.3_0                             0x00000ff4   Number         4  hw_jy901s.o(.text.i2c_wait_ack)
    [Anonymous Symbol]                       0x00000ff8   Section        0  hw_jy901s.o(.text.jy901s_init)
    __arm_cp.8_0                             0x0000108c   Number         4  hw_jy901s.o(.text.jy901s_init)
    __arm_cp.8_1                             0x00001090   Number         4  hw_jy901s.o(.text.jy901s_init)
    [Anonymous Symbol]                       0x00001094   Section        0  hw_jy901s.o(.text.jy901s_read_data)
    [Anonymous Symbol]                       0x00001170   Section        0  hw_jy901s.o(.text.jy901s_write_reg)
    [Anonymous Symbol]                       0x00001224   Section        0  empty.o(.text.main)
    __arm_cp.0_0                             0x00001264   Number         4  empty.o(.text.main)
    [Anonymous Symbol]                       0x00001268   Section        0  hw_ssd1306.o(.text.oled_clear)
    __arm_cp.7_0                             0x00001278   Number         4  hw_ssd1306.o(.text.oled_clear)
    [Anonymous Symbol]                       0x0000127c   Section        0  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_0                            0x00001310   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_1                            0x00001314   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_2                            0x00001318   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_3                            0x0000131c   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    __arm_cp.13_4                            0x00001320   Number         4  hw_ssd1306.o(.text.oled_display_attitude)
    [Anonymous Symbol]                       0x00001324   Section        0  hw_ssd1306.o(.text.oled_draw_char)
    __arm_cp.10_0                            0x000013f8   Number         4  hw_ssd1306.o(.text.oled_draw_char)
    [Anonymous Symbol]                       0x000013fc   Section        0  hw_ssd1306.o(.text.oled_draw_number)
    __arm_cp.12_0                            0x00001560   Number         4  hw_ssd1306.o(.text.oled_draw_number)
    [Anonymous Symbol]                       0x00001564   Section        0  hw_ssd1306.o(.text.oled_draw_string)
    oled_i2c_send_byte                       0x000015b9   Thumb Code   168  hw_ssd1306.o(.text.oled_i2c_send_byte)
    [Anonymous Symbol]                       0x000015b8   Section        0  hw_ssd1306.o(.text.oled_i2c_send_byte)
    oled_i2c_start                           0x00001661   Thumb Code    88  hw_ssd1306.o(.text.oled_i2c_start)
    [Anonymous Symbol]                       0x00001660   Section        0  hw_ssd1306.o(.text.oled_i2c_start)
    oled_i2c_stop                            0x000016b9   Thumb Code    94  hw_ssd1306.o(.text.oled_i2c_stop)
    [Anonymous Symbol]                       0x000016b8   Section        0  hw_ssd1306.o(.text.oled_i2c_stop)
    oled_i2c_wait_ack                        0x00001717   Thumb Code   132  hw_ssd1306.o(.text.oled_i2c_wait_ack)
    [Anonymous Symbol]                       0x00001716   Section        0  hw_ssd1306.o(.text.oled_i2c_wait_ack)
    [Anonymous Symbol]                       0x0000179c   Section        0  hw_ssd1306.o(.text.oled_init)
    __arm_cp.6_0                             0x0000184c   Number         4  hw_ssd1306.o(.text.oled_init)
    [Anonymous Symbol]                       0x00001850   Section        0  hw_ssd1306.o(.text.oled_set_pixel)
    [Anonymous Symbol]                       0x000018c8   Section        0  hw_ssd1306.o(.text.oled_update_display)
    __arm_cp.8_0                             0x00001924   Number         4  hw_ssd1306.o(.text.oled_update_display)
    __arm_cp.8_1                             0x00001928   Number         4  hw_ssd1306.o(.text.oled_update_display)
    [Anonymous Symbol]                       0x0000192c   Section        0  hw_ssd1306.o(.text.oled_write_cmd)
    [Anonymous Symbol]                       0x00001962   Section        0  hw_ssd1306.o(.text.oled_write_data)
    i.__ARM_clz                              0x00001998   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x000019c6   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x000019d4   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x000019d6   Section       14  handlers.o(i.__scatterload_zeroinit)
    [Anonymous Symbol]                       0x00001fe4   Section        0  hw_ssd1306.o(.rodata.str1.1)
    oled_buffer                              0x20000014   Data        1024  hw_ssd1306.o(.bss.oled_buffer)
    [Anonymous Symbol]                       0x20000014   Section        0  hw_ssd1306.o(.bss.oled_buffer)
    STACK                                    0x20000418   Section     1024  startup_mspm0g3507.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g3507.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x00000084   Data           0  startup_mspm0g3507.o(RESET)
    __Vectors_Size                           0x00000084   Number         0  startup_mspm0g3507.o ABSOLUTE
    __main                                   0x00000085   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00000085   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00000089   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x0000008d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x0000008d   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x0000008d   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x0000008d   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x00000095   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x00000095   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x00000099   Thumb Code     8  startup_mspm0g3507.o(.text)
    NMI_Handler                              0x000000a1   Thumb Code     2  startup_mspm0g3507.o(.text)
    HardFault_Handler                        0x000000a3   Thumb Code     2  startup_mspm0g3507.o(.text)
    SVC_Handler                              0x000000a5   Thumb Code     2  startup_mspm0g3507.o(.text)
    PendSV_Handler                           0x000000a7   Thumb Code     2  startup_mspm0g3507.o(.text)
    SysTick_Handler                          0x000000a9   Thumb Code     2  startup_mspm0g3507.o(.text)
    ADC0_IRQHandler                          0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    ADC1_IRQHandler                          0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    CANFD_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    DAC_IRQHandler                           0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    GROUP0_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    GROUP1_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    I2C_INST_IRQHandler                      0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    SPI_INST_IRQHandler                      0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMA0_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMA1_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG0_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG12_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG14_IRQHandler                        0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG6_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG7_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    TIMG8_IRQHandler                         0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    UART_INST_IRQHandler                     0x000000ab   Thumb Code     0  startup_mspm0g3507.o(.text)
    __aeabi_memset                           0x000000b5   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000b5   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000b5   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000c3   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000c3   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000c3   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000c7   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x000000d9   Thumb Code   162  fadd.o(.text)
    __aeabi_fsub                             0x0000017b   Thumb Code     8  fadd.o(.text)
    __aeabi_frsub                            0x00000183   Thumb Code     8  fadd.o(.text)
    __aeabi_fmul                             0x0000018b   Thumb Code   122  fmul.o(.text)
    __aeabi_dadd                             0x00000205   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x0000034d   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x00000359   Thumb Code    12  dadd.o(.text)
    __aeabi_dmul                             0x00000369   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x00000439   Thumb Code   234  ddiv.o(.text)
    __aeabi_fcmpge                           0x00000529   Thumb Code    28  fcmpge.o(.text)
    __aeabi_dcmple                           0x00000545   Thumb Code    44  dcmple.o(.text)
    __aeabi_dcmpge                           0x00000571   Thumb Code    44  dcmpge.o(.text)
    __aeabi_i2f                              0x0000059d   Thumb Code    22  fflti.o(.text)
    __aeabi_i2d                              0x000005b5   Thumb Code    34  dflti.o(.text)
    __aeabi_f2iz                             0x000005dd   Thumb Code    50  ffixi.o(.text)
    __aeabi_f2d                              0x0000060f   Thumb Code    40  f2d.o(.text)
    __aeabi_d2f                              0x00000637   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv$div0                       0x0000066f   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x0000066f   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_idiv                             0x000006ad   Thumb Code     0  idiv_div0.o(.text)
    __aeabi_idivmod                          0x000006ad   Thumb Code    74  idiv_div0.o(.text)
    __aeabi_llsl                             0x000006fd   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000006fd   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x0000071d   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x0000071d   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x00000743   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00000743   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00000753   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x000007c5   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x000007df   Thumb Code   164  depilogue.o(.text)
    __scatterload                            0x00000885   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000885   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x000008b5   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x000008b5   Thumb Code     0  llushr.o(.text)
    SYSCFG_DL_GPIO_init                      0x000009c1   Thumb Code   120  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_SYSCTL_init                    0x00000a39   Thumb Code     2  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_init                           0x00000a3b   Thumb Code    16  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00000a4d   Thumb Code    44  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SystemInit                               0x00000a79   Thumb Code     8  system_mspm0g3507.o(.text.SystemInit)
    delay_cycles                             0x00000a89   Thumb Code    26  ti_msp_dl_config.o(.text.delay_cycles)
    get_angle                                0x00000aa5   Thumb Code   460  hw_jy901s.o(.text.get_angle)
    jy901s_init                              0x00000ff9   Thumb Code   148  hw_jy901s.o(.text.jy901s_init)
    jy901s_read_data                         0x00001095   Thumb Code   220  hw_jy901s.o(.text.jy901s_read_data)
    jy901s_write_reg                         0x00001171   Thumb Code   180  hw_jy901s.o(.text.jy901s_write_reg)
    main                                     0x00001225   Thumb Code    64  empty.o(.text.main)
    oled_clear                               0x00001269   Thumb Code    16  hw_ssd1306.o(.text.oled_clear)
    oled_display_attitude                    0x0000127d   Thumb Code   148  hw_ssd1306.o(.text.oled_display_attitude)
    oled_draw_char                           0x00001325   Thumb Code   212  hw_ssd1306.o(.text.oled_draw_char)
    oled_draw_number                         0x000013fd   Thumb Code   356  hw_ssd1306.o(.text.oled_draw_number)
    oled_draw_string                         0x00001565   Thumb Code    84  hw_ssd1306.o(.text.oled_draw_string)
    oled_init                                0x0000179d   Thumb Code   176  hw_ssd1306.o(.text.oled_init)
    oled_set_pixel                           0x00001851   Thumb Code   120  hw_ssd1306.o(.text.oled_set_pixel)
    oled_update_display                      0x000018c9   Thumb Code    92  hw_ssd1306.o(.text.oled_update_display)
    oled_write_cmd                           0x0000192d   Thumb Code    54  hw_ssd1306.o(.text.oled_write_cmd)
    oled_write_data                          0x00001963   Thumb Code    54  hw_ssd1306.o(.text.oled_write_data)
    __ARM_clz                                0x00001999   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x000019c7   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x000019d5   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x000019d7   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    font8x16                                 0x000019e4   Data        1536  hw_ssd1306.o(.rodata.font8x16)
    Region$$Table$$Base                      0x00002008   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00002028   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_mspm0g3507.o(.data.SystemCoreClock)
    angle_struct                             0x20000008   Data          12  hw_jy901s.o(.bss.angle_struct)
    __initial_sp                             0x20000818   Data           0  startup_mspm0g3507.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00000099

  Load Region LR_1 (Base: 0x00000000, Size: 0x0000202c, Max: 0xffffffff, ABSOLUTE)

    Execution Region ER_RO (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00002028, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x00000084   Data   RO          143    RESET               startup_mspm0g3507.o
    0x00000084   0x00000084   0x00000000   Code   RO          152  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x00000084   0x00000084   0x00000004   Code   RO          189    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x00000088   0x00000088   0x00000004   Code   RO          192    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x0000008c   0x0000008c   0x00000000   Code   RO          194    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x0000008c   0x0000008c   0x00000000   Code   RO          196    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x0000008c   0x0000008c   0x00000008   Code   RO          197    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x00000094   0x00000094   0x00000000   Code   RO          199    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x00000094   0x00000094   0x00000000   Code   RO          201    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x00000094   0x00000094   0x00000004   Code   RO          190    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x00000098   0x00000098   0x0000001c   Code   RO          144  * .text               startup_mspm0g3507.o
    0x000000b4   0x000000b4   0x00000024   Code   RO          161    .text               mc_p.l(memseta.o)
    0x000000d8   0x000000d8   0x000000b2   Code   RO          163    .text               mf_p.l(fadd.o)
    0x0000018a   0x0000018a   0x0000007a   Code   RO          165    .text               mf_p.l(fmul.o)
    0x00000204   0x00000204   0x00000164   Code   RO          167    .text               mf_p.l(dadd.o)
    0x00000368   0x00000368   0x000000d0   Code   RO          169    .text               mf_p.l(dmul.o)
    0x00000438   0x00000438   0x000000f0   Code   RO          171    .text               mf_p.l(ddiv.o)
    0x00000528   0x00000528   0x0000001c   Code   RO          173    .text               mf_p.l(fcmpge.o)
    0x00000544   0x00000544   0x0000002c   Code   RO          175    .text               mf_p.l(dcmple.o)
    0x00000570   0x00000570   0x0000002c   Code   RO          177    .text               mf_p.l(dcmpge.o)
    0x0000059c   0x0000059c   0x00000016   Code   RO          179    .text               mf_p.l(fflti.o)
    0x000005b2   0x000005b2   0x00000002   PAD
    0x000005b4   0x000005b4   0x00000028   Code   RO          181    .text               mf_p.l(dflti.o)
    0x000005dc   0x000005dc   0x00000032   Code   RO          183    .text               mf_p.l(ffixi.o)
    0x0000060e   0x0000060e   0x00000028   Code   RO          185    .text               mf_p.l(f2d.o)
    0x00000636   0x00000636   0x00000038   Code   RO          187    .text               mf_p.l(d2f.o)
    0x0000066e   0x0000066e   0x0000003e   Code   RO          205    .text               mc_p.l(uidiv_div0.o)
    0x000006ac   0x000006ac   0x00000050   Code   RO          207    .text               mc_p.l(idiv_div0.o)
    0x000006fc   0x000006fc   0x00000020   Code   RO          211    .text               mc_p.l(llshl.o)
    0x0000071c   0x0000071c   0x00000026   Code   RO          213    .text               mc_p.l(llsshr.o)
    0x00000742   0x00000742   0x00000000   Code   RO          215    .text               mc_p.l(iusefp.o)
    0x00000742   0x00000742   0x00000082   Code   RO          216    .text               mf_p.l(fepilogue.o)
    0x000007c4   0x000007c4   0x000000be   Code   RO          218    .text               mf_p.l(depilogue.o)
    0x00000882   0x00000882   0x00000002   PAD
    0x00000884   0x00000884   0x00000030   Code   RO          222    .text               mc_p.l(init.o)
    0x000008b4   0x000008b4   0x00000022   Code   RO          224    .text               mc_p.l(llushr.o)
    0x000008d6   0x000008d6   0x00000010   Code   RO           37    .text.DL_GPIO_clearPins  hw_jy901s.o
    0x000008e6   0x000008e6   0x00000010   Code   RO           89    .text.DL_GPIO_clearPins  hw_ssd1306.o
    0x000008f6   0x000008f6   0x00000014   Code   RO           35    .text.DL_GPIO_enableOutput  hw_jy901s.o
    0x0000090a   0x0000090a   0x00000014   Code   RO           87    .text.DL_GPIO_enableOutput  hw_ssd1306.o
    0x0000091e   0x0000091e   0x00000014   Code   RO          123    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x00000932   0x00000932   0x00000008   Code   RO          115    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x0000093a   0x0000093a   0x00000008   Code   RO           39    .text.DL_GPIO_initDigitalInput  hw_jy901s.o
    0x00000942   0x00000942   0x00000008   Code   RO           91    .text.DL_GPIO_initDigitalInput  hw_ssd1306.o
    0x0000094a   0x0000094a   0x00000008   Code   RO           31    .text.DL_GPIO_initDigitalOutput  hw_jy901s.o
    0x00000952   0x00000952   0x00000008   Code   RO           83    .text.DL_GPIO_initDigitalOutput  hw_ssd1306.o
    0x0000095a   0x0000095a   0x00000008   Code   RO          119    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000962   0x00000962   0x00000012   Code   RO           41    .text.DL_GPIO_readPins  hw_jy901s.o
    0x00000974   0x00000974   0x00000012   Code   RO           93    .text.DL_GPIO_readPins  hw_ssd1306.o
    0x00000986   0x00000986   0x00000008   Code   RO          113    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x0000098e   0x0000098e   0x00000010   Code   RO           33    .text.DL_GPIO_setPins  hw_jy901s.o
    0x0000099e   0x0000099e   0x00000010   Code   RO           85    .text.DL_GPIO_setPins  hw_ssd1306.o
    0x000009ae   0x000009ae   0x00000010   Code   RO          121    .text.DL_GPIO_setPins  ti_msp_dl_config.o
    0x000009be   0x000009be   0x00000002   PAD
    0x000009c0   0x000009c0   0x00000078   Code   RO          111    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x00000a38   0x00000a38   0x00000002   Code   RO          109    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x00000a3a   0x00000a3a   0x00000010   Code   RO          105    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00000a4a   0x00000a4a   0x00000002   PAD
    0x00000a4c   0x00000a4c   0x0000002c   Code   RO          107    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x00000a78   0x00000a78   0x00000010   Code   RO          134    .text.SystemInit    system_mspm0g3507.o
    0x00000a88   0x00000a88   0x0000001a   Code   RO          117    .text.delay_cycles  ti_msp_dl_config.o
    0x00000aa2   0x00000aa2   0x00000002   PAD
    0x00000aa4   0x00000aa4   0x000001e4   Code   RO           29    .text.get_angle     hw_jy901s.o
    0x00000c88   0x00000c88   0x000000a4   Code   RO           23    .text.i2c_read_byte  hw_jy901s.o
    0x00000d2c   0x00000d2c   0x00000094   Code   RO           25    .text.i2c_send_ack  hw_jy901s.o
    0x00000dc0   0x00000dc0   0x000000a8   Code   RO           15    .text.i2c_send_byte  hw_jy901s.o
    0x00000e68   0x00000e68   0x00000068   Code   RO           13    .text.i2c_start     hw_jy901s.o
    0x00000ed0   0x00000ed0   0x0000005c   Code   RO           19    .text.i2c_stop      hw_jy901s.o
    0x00000f2c   0x00000f2c   0x000000cc   Code   RO           17    .text.i2c_wait_ack  hw_jy901s.o
    0x00000ff8   0x00000ff8   0x0000009c   Code   RO           27    .text.jy901s_init   hw_jy901s.o
    0x00001094   0x00001094   0x000000dc   Code   RO           21    .text.jy901s_read_data  hw_jy901s.o
    0x00001170   0x00001170   0x000000b4   Code   RO           11    .text.jy901s_write_reg  hw_jy901s.o
    0x00001224   0x00001224   0x00000044   Code   RO            2    .text.main          empty.o
    0x00001268   0x00001268   0x00000014   Code   RO           69    .text.oled_clear    hw_ssd1306.o
    0x0000127c   0x0000127c   0x000000a8   Code   RO           81    .text.oled_display_attitude  hw_ssd1306.o
    0x00001324   0x00001324   0x000000d8   Code   RO           75    .text.oled_draw_char  hw_ssd1306.o
    0x000013fc   0x000013fc   0x00000168   Code   RO           79    .text.oled_draw_number  hw_ssd1306.o
    0x00001564   0x00001564   0x00000054   Code   RO           77    .text.oled_draw_string  hw_ssd1306.o
    0x000015b8   0x000015b8   0x000000a8   Code   RO           59    .text.oled_i2c_send_byte  hw_ssd1306.o
    0x00001660   0x00001660   0x00000058   Code   RO           57    .text.oled_i2c_start  hw_ssd1306.o
    0x000016b8   0x000016b8   0x0000005e   Code   RO           63    .text.oled_i2c_stop  hw_ssd1306.o
    0x00001716   0x00001716   0x00000084   Code   RO           61    .text.oled_i2c_wait_ack  hw_ssd1306.o
    0x0000179a   0x0000179a   0x00000002   PAD
    0x0000179c   0x0000179c   0x000000b4   Code   RO           67    .text.oled_init     hw_ssd1306.o
    0x00001850   0x00001850   0x00000078   Code   RO           73    .text.oled_set_pixel  hw_ssd1306.o
    0x000018c8   0x000018c8   0x00000064   Code   RO           71    .text.oled_update_display  hw_ssd1306.o
    0x0000192c   0x0000192c   0x00000036   Code   RO           55    .text.oled_write_cmd  hw_ssd1306.o
    0x00001962   0x00001962   0x00000036   Code   RO           65    .text.oled_write_data  hw_ssd1306.o
    0x00001998   0x00001998   0x0000002e   Code   RO          220    i.__ARM_clz         mf_p.l(depilogue.o)
    0x000019c6   0x000019c6   0x0000000e   Code   RO          228    i.__scatterload_copy  mc_p.l(handlers.o)
    0x000019d4   0x000019d4   0x00000002   Code   RO          229    i.__scatterload_null  mc_p.l(handlers.o)
    0x000019d6   0x000019d6   0x0000000e   Code   RO          230    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x000019e4   0x000019e4   0x00000600   Data   RO           95    .rodata.font8x16    hw_ssd1306.o
    0x00001fe4   0x00001fe4   0x00000022   Data   RO           97    .rodata.str1.1      hw_ssd1306.o
    0x00002006   0x00002006   0x00000002   PAD
    0x00002008   0x00002008   0x00000020   Data   RO          227    Region$$Table       anon$$obj.o


    Execution Region ER_RW (Exec base: 0x20000000, Load base: 0x00002028, Size: 0x00000004, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00002028   0x00000004   Data   RW          136    .data.SystemCoreClock  system_mspm0g3507.o


    Execution Region ER_ZI (Exec base: 0x20000008, Load base: 0x0000202c, Size: 0x00000810, Max: 0xffffffff, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000008        -       0x0000000c   Zero   RW           46    .bss.angle_struct   hw_jy901s.o
    0x20000014        -       0x00000400   Zero   RW           96    .bss.oled_buffer    hw_ssd1306.o
    0x20000414   0x0000202c   0x00000004   PAD
    0x20000418        -       0x00000400   Zero   RW          145    STACK               startup_mspm0g3507.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        68          4          0          0          0        739   empty.o
      2006         40          0          0         12       4470   hw_jy901s.o
      1924         44       1570          0       1024       5084   hw_ssd1306.o
        28          8        132          0       1024        644   startup_mspm0g3507.o
        16          8          0          4          0        660   system_mspm0g3507.o
       268          0          0          0          0       1747   ti_msp_dl_config.o

    ----------------------------------------------------------------------
      4318        <USER>       <GROUP>          4       2064      13344   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         8          0          2          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        80          6          0          0          0         72   idiv_div0.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
        62          0          0          0          0         72   uidiv_div0.o
        56          0          0          0          0         68   d2f.o
       356          4          0          0          0        140   dadd.o
        44          0          0          0          0         68   dcmpge.o
        44          0          0          0          0         68   dcmple.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        40          6          0          0          0         68   dflti.o
       208          6          0          0          0         88   dmul.o
        40          0          0          0          0         60   f2d.o
       178          0          0          0          0        108   fadd.o
        28          0          0          0          0         60   fcmpge.o
       130          0          0          0          0        144   fepilogue.o
        50          0          0          0          0         60   ffixi.o
        22          0          0          0          0         68   fflti.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      2178         <USER>          <GROUP>          0          0       1888   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       380         24          0          0          0        516   mc_p.l
      1794         22          0          0          0       1372   mf_p.l

    ----------------------------------------------------------------------
      2178         <USER>          <GROUP>          0          0       1888   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      6496        150       1736          4       2064      14424   Grand Totals
      6496        150       1736          4       2064      14424   ELF Image Totals
      6496        150       1736          4          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 8232 (   8.04kB)
    Total RW  Size (RW Data + ZI Data)              2068 (   2.02kB)
    Total ROM Size (Code + RO Data + RW Data)       8236 (   8.04kB)

==============================================================================

