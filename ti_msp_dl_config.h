#ifndef TI_MSP_DL_CONFIG_H_
#define TI_MSP_DL_CONFIG_H_

// 注意：在Keil5环境中，需要安装MSPM0G3507的Device Pack
// 并根据实际的DriverLib路径调整以下包含文件
#include <stdint.h>
// #include <ti/driverlib/driverlib.h>  // 需要TI DriverLib支持

// ARM CMSIS核心定义
#ifndef __NOP
#define __NOP() __asm volatile ("nop")
#endif

#ifdef __cplusplus
extern "C" {
#endif

// 系统时钟频率定义
#define CPUCLK_FREQ                     32000000

// 基本类型定义（临时解决方案）
typedef struct {
    volatile uint32_t DATA;
    volatile uint32_t DIR;
    volatile uint32_t SET;
    volatile uint32_t CLR;
} GPIO_Type;

// GPIO基地址定义（需要根据实际芯片手册调整）
#define GPIOA_BASE                      0x40000000UL
#define GPIOB_BASE                      0x40001000UL
#define GPIOA                           ((GPIO_Type*)GPIOA_BASE)
#define GPIOB                           ((GPIO_Type*)GPIOB_BASE)

// GPIO引脚定义
#define DL_GPIO_PIN_9                   (1UL << 9)
#define DL_GPIO_PIN_12                  (1UL << 12)
#define DL_GPIO_PIN_13                  (1UL << 13)
#define DL_GPIO_PIN_15                  (1UL << 15)
#define DL_GPIO_PIN_16                  (1UL << 16)
#define DL_GPIO_PIN_18                  (1UL << 18)
#define DL_GPIO_PIN_19                  (1UL << 19)
#define DL_GPIO_PIN_20                  (1UL << 20)
#define DL_GPIO_PIN_26                  (1UL << 26)
#define DL_GPIO_PIN_27                  (1UL << 27)

// IOMUX定义（临时）
#define IOMUX_PINCM25                   25
#define IOMUX_PINCM19                   19
#define IOMUX_PINCM13                   13
#define IOMUX_PINCM14                   14
#define IOMUX_PINCM16                   16
#define IOMUX_PINCM17                   17
#define IOMUX_PINCM27                   27

// GPIO引脚定义 - JY901S I2C接口
// SDA引脚：PB9
#define GYRO_I2C_SDA_PORT               GPIOB
#define GYRO_I2C_SDA_PIN                DL_GPIO_PIN_9
#define GYRO_I2C_SDA_IOMUX              (IOMUX_PINCM25)

// SCL引脚：PA18
#define GYRO_I2C_SCL_PORT               GPIOA
#define GYRO_I2C_SCL_PIN                DL_GPIO_PIN_18
#define GYRO_I2C_SCL_IOMUX              (IOMUX_PINCM19)

// OLED I2C接口引脚定义
// SDA引脚：PA12
#define OLED_I2C_SDA_PORT               GPIOA
#define OLED_I2C_SDA_PIN                DL_GPIO_PIN_12
#define OLED_I2C_SDA_IOMUX              (IOMUX_PINCM13)

// SCL引脚：PA13
#define OLED_I2C_SCL_PORT               GPIOA
#define OLED_I2C_SCL_PIN                DL_GPIO_PIN_13
#define OLED_I2C_SCL_IOMUX              (IOMUX_PINCM14)

// LED指示灯引脚定义（用于调试）- 使用板载RGB LED红色通道
#define DEBUG_LED_PORT                  GPIOB
#define DEBUG_LED_PIN                   DL_GPIO_PIN_26
#define DEBUG_LED_IOMUX                 (27)

// 调试接口引脚定义
#define DEBUGSS_SWCLK_PORT              GPIOA
#define DEBUGSS_SWCLK_PIN               DL_GPIO_PIN_20
#define DEBUGSS_SWDIO_PORT              GPIOA
#define DEBUGSS_SWDIO_PIN               DL_GPIO_PIN_19

// GPIO操作函数（简化实现）
static inline void DL_GPIO_setPins(GPIO_Type* port, uint32_t pins) {
    port->SET = pins;
}

static inline void DL_GPIO_clearPins(GPIO_Type* port, uint32_t pins) {
    port->CLR = pins;
}

static inline uint32_t DL_GPIO_readPins(GPIO_Type* port, uint32_t pins) {
    return (port->DATA & pins);
}

static inline void DL_GPIO_enableOutput(GPIO_Type* port, uint32_t pins) {
    port->DIR |= pins;
}

static inline void DL_GPIO_initDigitalOutput(uint32_t iomux) {
    (void)iomux; // 避免未使用参数警告
    // 简化的IOMUX配置，实际需要根据芯片手册实现
}

static inline void DL_GPIO_initDigitalInput(uint32_t iomux) {
    (void)iomux; // 避免未使用参数警告
    // 简化的IOMUX配置，实际需要根据芯片手册实现
}

// 电源管理函数（简化）
static inline void DL_GPIO_reset(GPIO_Type* port) {
    (void)port; // 避免未使用参数警告
    // 简化实现
}

static inline void DL_GPIO_enablePower(GPIO_Type* port) {
    (void)port; // 避免未使用参数警告
    // 简化实现
}

// 函数声明
void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);

// 延时函数声明
void delay_cycles(uint32_t cycles);

#ifdef __cplusplus
}
#endif

#endif /* TI_MSP_DL_CONFIG_H_ */
