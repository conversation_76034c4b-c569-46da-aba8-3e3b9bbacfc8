Dependencies for Project 'jy901s', Target 'Target 1': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (.\empty.c)(0x68612A8D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./hardware

-ID:/Keil/ARM/PACK/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1/Device/Include

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/empty.o -MMD)
I (ti_msp_dl_config.h)(0x68612D79)
I (hardware\hw_jy901s.h)(0x68612D52)
I (hardware\..\ti_msp_dl_config.h)(0x68612D79)
F (.\hardware\hw_jy901s.c)(0x68612AAD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./hardware

-ID:/Keil/ARM/PACK/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1/Device/Include

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/hw_jy901s.o -MMD)
I (hardware\hw_jy901s.h)(0x68612D52)
I (hardware\..\ti_msp_dl_config.h)(0x68612D79)
F (.\ti_msp_dl_config.c)(0x68612B6F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./hardware

-ID:/Keil/ARM/PACK/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1/Device/Include

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (ti_msp_dl_config.h)(0x68612D79)
F (.\system_mspm0g3507.c)(0x68612AF3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O0 -ffunction-sections -Wall -Wextra -Wno-packed -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ./hardware

-ID:/Keil/ARM/PACK/TexasInstruments/MSPM0G1X0X_G3X0X_DFP/1.3.1/Device/Include

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/system_mspm0g3507.o -MMD)
I (ti_msp_dl_config.h)(0x68612D79)
F (.\startup_mspm0g3507.s)(0x68613153)(--cpu Cortex-M0+ -g --pd "__MICROLIB SETA 1" --diag_suppress=A1950W

-ID:\Keil\ARM\PACK\TexasInstruments\MSPM0G1X0X_G3X0X_DFP\1.3.1\Device\Include

--pd "__UVISION_VERSION SETA 540"

--pd "__MSPM0G3507__ SETA 1"

--list .\listings\startup_mspm0g3507.lst

--xref -o .\objects\startup_mspm0g3507.o

--depend .\objects\startup_mspm0g3507.d)
F (.\hardware\hw_jy901s.h)(0x68612D52)()
F (.\ti_msp_dl_config.h)(0x68612D79)()
