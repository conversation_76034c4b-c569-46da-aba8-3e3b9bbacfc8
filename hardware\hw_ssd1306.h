#ifndef _HW_SSD1306_H_
#define _HW_SSD1306_H_

#include "../ti_msp_dl_config.h"
#include "hw_jy901s.h"

// 延时函数宏定义（复用JY901S中的定义）
#ifndef delay_us
#define delay_us(__us)  delay_cycles((CPUCLK_FREQ/1000000)*__us)
#endif
#ifndef delay_ms
#define delay_ms(__ms)  delay_cycles((CPUCLK_FREQ/1000)*__ms)
#endif

// SSD1306 OLED显示屏参数
#define SSD1306_I2C_ADDRESS             0x3C    // SSD1306 I2C地址
#define SSD1306_WIDTH                   128     // 屏幕宽度
#define SSD1306_HEIGHT                  64      // 屏幕高度
#define SSD1306_BUFFER_SIZE             (SSD1306_WIDTH * SSD1306_HEIGHT / 8)

// SSD1306命令定义
#define SSD1306_SETCONTRAST             0x81
#define SSD1306_DISPLAYALLON_RESUME     0xA4
#define SSD1306_DISPLAYALLON            0xA5
#define SSD1306_NORMALDISPLAY           0xA6
#define SSD1306_INVERTDISPLAY           0xA7
#define SSD1306_DISPLAYOFF              0xAE
#define SSD1306_DISPLAYON               0xAF
#define SSD1306_SETDISPLAYOFFSET        0xD3
#define SSD1306_SETCOMPINS              0xDA
#define SSD1306_SETVCOMDETECT           0xDB
#define SSD1306_SETDISPLAYCLOCKDIV      0xD5
#define SSD1306_SETPRECHARGE            0xD9
#define SSD1306_SETMULTIPLEX            0xA8
#define SSD1306_SETLOWCOLUMN            0x00
#define SSD1306_SETHIGHCOLUMN           0x10
#define SSD1306_SETSTARTLINE            0x40
#define SSD1306_MEMORYMODE              0x20
#define SSD1306_COLUMNADDR              0x21
#define SSD1306_PAGEADDR                0x22
#define SSD1306_COMSCANINC              0xC0
#define SSD1306_COMSCANDEC              0xC8
#define SSD1306_SEGREMAP                0xA0
#define SSD1306_CHARGEPUMP              0x8D

// OLED I2C操作宏定义（复用JY901S的I2C实现模式）
#define OLED_SDA_OUT()   {                                                  \
                        DL_GPIO_initDigitalOutput(OLED_I2C_SDA_IOMUX);     \
                        DL_GPIO_setPins(OLED_I2C_SDA_PORT, OLED_I2C_SDA_PIN);      \
                        DL_GPIO_enableOutput(OLED_I2C_SDA_PORT, OLED_I2C_SDA_PIN); \
                    }

#define OLED_SDA_IN()    { DL_GPIO_initDigitalInput(OLED_I2C_SDA_IOMUX); }

#define OLED_SDA_GET()   ( ( ( DL_GPIO_readPins(OLED_I2C_SDA_PORT,OLED_I2C_SDA_PIN) & OLED_I2C_SDA_PIN ) > 0 ) ? 1 : 0 )

#define OLED_SDA(x)      ( (x) ? (DL_GPIO_setPins(OLED_I2C_SDA_PORT,OLED_I2C_SDA_PIN)) : (DL_GPIO_clearPins(OLED_I2C_SDA_PORT,OLED_I2C_SDA_PIN)) )
#define OLED_SCL(x)      ( (x) ? (DL_GPIO_setPins(OLED_I2C_SCL_PORT,OLED_I2C_SCL_PIN)) : (DL_GPIO_clearPins(OLED_I2C_SCL_PORT,OLED_I2C_SCL_PIN)) )

// 颜色定义
#define OLED_COLOR_BLACK                0
#define OLED_COLOR_WHITE                1

// 函数声明
void oled_init(void);
void oled_clear(void);
void oled_update_display(void);
void oled_set_pixel(uint8_t x, uint8_t y, uint8_t color);
void oled_draw_char(uint8_t x, uint8_t y, char ch);
void oled_draw_string(uint8_t x, uint8_t y, const char* str);
void oled_draw_number(uint8_t x, uint8_t y, float num, uint8_t decimal);
void oled_display_attitude(GYRO_ANGLE_DATA_STRUCT* data);

// 内部函数声明
void oled_write_cmd(uint8_t cmd);
void oled_write_data(uint8_t data);

#endif /* _HW_SSD1306_H_ */
