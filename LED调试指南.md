# LED调试指南 - LP-MSPM0G3507

## 🔍 **观察哪个LED？**

### LP-MSPM0G3507开发板LED位置

在LP-MSPM0G3507 LaunchPad开发板上，有以下LED：

#### 1. 板载RGB LED（我们使用的调试LED）
- **位置**: 开发板右上角，靠近USB连接器
- **颜色**: RGB三色LED
- **我们使用**: **红色通道** (PB26引脚)
- **标识**: 通常标记为"LED2"或"RGB"

#### 2. 电源指示LED
- **位置**: 开发板左侧，靠近电源开关
- **颜色**: 通常为绿色
- **功能**: 指示开发板供电状态
- **说明**: 这个不是我们的调试LED

## 📍 **具体观察位置**

```
LP-MSPM0G3507 LaunchPad 布局：

    USB接口
    ┌─────┐
    │     │  ← 这里附近有RGB LED (我们的调试LED)
    │     │
    │     │
    │ MCU │
    │     │
    │     │
    └─────┘
    
电源LED → ●  (左侧，电源指示，常亮绿色)
调试LED → ●  (右上角，RGB LED的红色，我们控制的)
```

## 🚨 **重要说明**

### 我们控制的是：
- **RGB LED的红色通道**
- **位置**: 开发板右上角
- **引脚**: PB26
- **颜色**: 红色

### 不是我们控制的：
- 电源指示LED（左侧绿色，常亮）
- USB通信LED（如果有）

## 🔍 **LED状态观察指南**

### 正常启动序列（观察右上角RGB LED的红色）：

1. **系统启动** (程序开始运行)
   - 红色LED **长亮1秒**
   - 然后熄灭1秒

2. **JY901S初始化完成**
   - 红色LED **短亮0.25秒**
   - 然后熄灭0.25秒

3. **OLED初始化完成**
   - 红色LED **长亮1秒**
   - 然后熄灭

4. **程序正常运行**
   - 每5秒红色LED **短闪一次**（心跳）

### 异常情况判断：

#### 情况1: LED完全不亮
- **原因**: 程序未运行
- **检查**: 
  - 程序是否正确下载
  - 开发板是否正常供电
  - 复位按钮是否按过

#### 情况2: 只有启动长亮，然后不再闪烁
- **原因**: 程序卡在JY901S初始化
- **检查**: 
  - JY901S连接是否正确
  - PB9和PA18连接

#### 情况3: 有心跳闪烁，但OLED不亮
- **原因**: 程序正常，OLED硬件问题
- **检查**: 
  - OLED电源连接
  - PA12和PA13连接
  - OLED模块是否损坏

## 🔧 **如果找不到LED**

### 方法1: 查看开发板丝印
在开发板上寻找以下标记：
- "LED2"
- "RGB"
- "D2"
- 或者靠近USB接口的小LED

### 方法2: 使用万用表
1. 将万用表设置为二极管档
2. 测试开发板上的小LED
3. 找到能够导通的LED（正向压降约1.8-2.2V）

### 方法3: 修改为其他引脚
如果确实找不到LED，可以外接一个LED：

```c
// 在ti_msp_dl_config.h中修改为其他可用引脚
#define DEBUG_LED_PORT    GPIOA
#define DEBUG_LED_PIN     DL_GPIO_PIN_0  // 改为其他引脚
```

然后在该引脚外接LED和限流电阻。

## 📋 **快速检查清单**

- [ ] 找到开发板右上角的RGB LED
- [ ] 确认观察的是红色通道
- [ ] 程序重新编译下载
- [ ] 按复位按钮重启程序
- [ ] 观察LED启动序列
- [ ] 记录LED的具体表现

## 🚀 **下一步**

现在请：
1. **找到开发板右上角的RGB LED**
2. **重新编译下载程序**
3. **按复位按钮**
4. **观察红色LED的闪烁模式**
5. **告诉我具体看到的LED表现**

这样我就能准确判断问题出在哪个环节了！
