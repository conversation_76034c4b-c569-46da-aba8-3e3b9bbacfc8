# JY901S + OLED 硬件连接指南

## 项目概述

本项目实现了JY901S九轴陀螺仪传感器与SSD1306 OLED显示屏的集成，能够实时显示Roll、Pitch、Yaw三轴姿态角度数据。

## 硬件清单

### 必需硬件
1. **LP-MSPM0G3507 LaunchPad开发板** - 主控制器
2. **JY901S九轴陀螺仪传感器** - 姿态检测
3. **0.96寸OLED显示屏(SSD1306)** - 数据显示
4. **杜邦线若干** - 连接线
5. **面包板(可选)** - 便于连接

### 可选硬件
- **电源模块** - 如需独立供电
- **外壳** - 保护电路

## 详细连接方案

### 🔌 **引脚分配表**

| 功能 | 器件 | 引脚 | LP-MSPM0G3507引脚 | 说明 |
|------|------|------|-------------------|------|
| **电源** | JY901S | VCC | 3.3V | 传感器电源正极 |
| | JY901S | GND | GND | 传感器电源负极 |
| | OLED | VCC | 3.3V | 显示屏电源正极 |
| | OLED | GND | GND | 显示屏电源负极 |
| **JY901S I2C** | JY901S | SDA | PB9 | 数据线 |
| | JY901S | SCL | PA18 | 时钟线 |
| **OLED I2C** | OLED | SDA | PA12 | 数据线 |
| | OLED | SCL | PA13 | 时钟线 |
| **调试接口** | XDS110 | SWDIO | PA19 | 调试数据 |
| | XDS110 | SWCLK | PA20 | 调试时钟 |

### 🔗 **连接步骤**

#### 步骤1：电源连接
```
LP-MSPM0G3507 3.3V  ──┬── JY901S VCC
                       └── OLED VCC

LP-MSPM0G3507 GND   ──┬── JY901S GND  
                       └── OLED GND
```

#### 步骤2：JY901S传感器连接
```
LP-MSPM0G3507 PB9   ──── JY901S SDA
LP-MSPM0G3507 PA18  ──── JY901S SCL
```

#### 步骤3：OLED显示屏连接
```
LP-MSPM0G3507 PA12  ──── OLED SDA
LP-MSPM0G3507 PA13  ──── OLED SCL
```

## 🖼️ **连接示意图**

```
    LP-MSPM0G3507 LaunchPad
    ┌─────────────────────────┐
    │                         │
    │  PA12 ●────────────● SDA│  OLED
    │  PA13 ●────────────● SCL│  (SSD1306)
    │  3.3V ●────────────● VCC│  0.96"
    │   GND ●────────────● GND│
    │                         │
    │  PB9  ●────────────● SDA│  JY901S
    │  PA18 ●────────────● SCL│  九轴传感器
    │  3.3V ●────────────● VCC│
    │   GND ●────────────● GND│
    │                         │
    │  PA19 ●─── SWDIO        │  调试接口
    │  PA20 ●─── SWCLK        │  (XDS110)
    │                         │
    └─────────────────────────┘
```

## ⚡ **电气特性**

### 电源要求
- **工作电压**: 3.3V
- **总电流消耗**: 约50-80mA
  - LP-MSPM0G3507: ~20mA
  - JY901S: ~15-25mA  
  - OLED: ~15-30mA

### I2C总线规格
- **时钟频率**: 100kHz (标准模式)
- **电平标准**: 3.3V CMOS
- **上拉电阻**: 内部上拉 (约47kΩ)

## 🔧 **安装注意事项**

### 1. 连接顺序
1. **断电状态下进行所有连接**
2. 先连接电源线(VCC, GND)
3. 再连接信号线(SDA, SCL)
4. 最后连接调试接口

### 2. 线缆要求
- **长度**: 建议不超过20cm
- **规格**: 24-26 AWG杜邦线
- **质量**: 确保接触良好，避免虚接

### 3. 布线建议
- **避免交叉**: I2C信号线避免与电源线平行走线
- **屏蔽干扰**: 远离高频信号源
- **固定连接**: 使用面包板或焊接确保连接稳定

## 🧪 **测试验证**

### 硬件测试步骤
1. **上电检查**
   - 用万用表测量各器件电压是否为3.3V
   - 检查是否有短路现象

2. **I2C通信测试**
   - 编译并下载程序
   - 观察OLED是否正常显示

3. **功能验证**
   - 旋转JY901S传感器
   - 观察OLED上角度数值是否实时变化

### 预期显示效果
```
┌─────────────────────┐
│   Attitude Data     │
│                     │
│ Roll:  -12.5 deg    │
│ Pitch:  45.2 deg    │
│ Yaw:   180.0 deg    │
│                     │
└─────────────────────┘
```

## 🚨 **故障排除**

### 常见问题及解决方案

| 问题现象 | 可能原因 | 解决方案 |
|----------|----------|----------|
| OLED无显示 | 电源未连接 | 检查VCC/GND连接 |
| | I2C地址错误 | 确认OLED地址为0x3C |
| | 连线错误 | 检查SDA/SCL连接 |
| 角度数据不变 | JY901S未工作 | 检查传感器电源和I2C连接 |
| | 程序未运行 | 检查下载和调试配置 |
| 显示乱码 | 字体数据错误 | 重新编译程序 |
| | 缓冲区问题 | 检查内存配置 |
| 数据跳动 | I2C干扰 | 改善布线，添加滤波电容 |
| | 电源不稳定 | 检查电源质量 |

## 📋 **技术规格总结**

### 系统参数
- **主控**: MSPM0G3507 (Cortex-M0+, 32MHz)
- **传感器**: JY901S (9轴IMU)
- **显示**: SSD1306 OLED (128x64像素)
- **通信**: 双I2C总线
- **刷新率**: 10Hz
- **精度**: 0.1度

### 性能指标
- **启动时间**: <2秒
- **响应时间**: <100ms
- **角度范围**: ±180度
- **显示延迟**: <100ms

## 📞 **技术支持**

如遇到问题，请检查：
1. 硬件连接是否正确
2. 电源电压是否稳定
3. 程序是否正确下载
4. I2C地址是否匹配

更多技术细节请参考：
- `README_Keil5.md` - Keil5编译指南
- `编译修复说明.md` - 编译问题解决
- 源代码注释 - 详细实现说明
