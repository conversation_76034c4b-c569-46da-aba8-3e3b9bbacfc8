#ifndef _HW_FONTS_H_
#define _HW_FONTS_H_

#include <stdint.h>

// 8x16像素字体定义
#define FONT_WIDTH  8
#define FONT_HEIGHT 16

// ASCII字符字体数据 (8x16像素)
// 每个字符占用16字节，按列存储
const uint8_t font8x16[][16] = {
    // 空格 (0x20)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // ! (0x21)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0xE0,0x1F,0xE0,0x00,0x00,0x00,0x00,0x00,0x00},
    // " (0x22)
    {0x00,0x00,0x00,0x0E,0x00,0x0E,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x0E,0x00,0x00},
    // # (0x23)
    {0x00,0x00,0x06,0x60,0x3F,0xF8,0x06,0x60,0x06,0x60,0x3F,0xF8,0x06,0x60,0x00,0x00},
    // $ (0x24)
    {0x00,0x00,0x0C,0x30,0x1E,0x70,0x33,0x60,0x33,0x60,0x1C,0xF0,0x18,0x60,0x00,0x00},
    // % (0x25)
    {0x00,0x00,0x1C,0x18,0x36,0x30,0x36,0x60,0x1C,0xC0,0x01,0x80,0x03,0x00,0x00,0x00},
    // & (0x26)
    {0x00,0x00,0x0F,0x80,0x19,0xC0,0x30,0x60,0x30,0x60,0x19,0xC0,0x0F,0x80,0x00,0x00},
    // ' (0x27)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x0E,0x00,0x00,0x00,0x00,0x00,0x00},
    // ( (0x28)
    {0x00,0x00,0x00,0x00,0x07,0xE0,0x1F,0xF8,0x38,0x1C,0x20,0x04,0x00,0x00,0x00,0x00},
    // ) (0x29)
    {0x00,0x00,0x00,0x00,0x20,0x04,0x38,0x1C,0x1F,0xF8,0x07,0xE0,0x00,0x00,0x00,0x00},
    // * (0x2A)
    {0x00,0x00,0x02,0x40,0x03,0xC0,0x00,0xF0,0x00,0xF0,0x03,0xC0,0x02,0x40,0x00,0x00},
    // + (0x2B)
    {0x00,0x00,0x01,0x80,0x01,0x80,0x07,0xE0,0x07,0xE0,0x01,0x80,0x01,0x80,0x00,0x00},
    // , (0x2C)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x38,0x00,0x18,0x00,0x00,0x00,0x00,0x00},
    // - (0x2D)
    {0x00,0x00,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x00,0x00},
    // . (0x2E)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // / (0x2F)
    {0x00,0x00,0x30,0x00,0x18,0x00,0x0C,0x00,0x06,0x00,0x03,0x00,0x01,0x80,0x00,0x00},
    // 0 (0x30)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x30,0x30,0x30,0x30,0x30,0x30,0x1F,0xE0,0x0F,0xC0},
    // 1 (0x31)
    {0x00,0x00,0x00,0x00,0x30,0x60,0x30,0x60,0x3F,0xF0,0x3F,0xF0,0x30,0x00,0x30,0x00},
    // 2 (0x32)
    {0x00,0x00,0x30,0x60,0x38,0x70,0x3C,0x30,0x36,0x30,0x33,0x30,0x31,0xE0,0x30,0xC0},
    // 3 (0x33)
    {0x00,0x00,0x18,0x60,0x38,0x70,0x33,0x30,0x33,0x30,0x33,0x30,0x1F,0xE0,0x0C,0xC0},
    // 4 (0x34)
    {0x00,0x00,0x07,0x00,0x07,0x80,0x06,0xC0,0x06,0x60,0x3F,0xF0,0x3F,0xF0,0x06,0x00},
    // 5 (0x35)
    {0x00,0x00,0x19,0xF0,0x39,0xF0,0x31,0x80,0x31,0x80,0x31,0x80,0x1F,0x80,0x0F,0x00},
    // 6 (0x36)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x33,0x30,0x33,0x30,0x33,0x30,0x1E,0x60,0x0C,0x00},
    // 7 (0x37)
    {0x00,0x00,0x01,0x80,0x01,0x80,0x31,0x80,0x39,0x80,0x0F,0x80,0x07,0x80,0x01,0x80},
    // 8 (0x38)
    {0x00,0x00,0x1C,0xE0,0x3F,0xF0,0x33,0x30,0x33,0x30,0x33,0x30,0x3F,0xF0,0x1C,0xE0},
    // 9 (0x39)
    {0x00,0x00,0x01,0xC0,0x33,0xE0,0x33,0x30,0x33,0x30,0x33,0x30,0x1F,0xE0,0x0F,0xC0},
    // : (0x3A)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x70,0x1C,0x70,0x00,0x00,0x00,0x00,0x00,0x00},
    // ; (0x3B)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x70,0x38,0x70,0x18,0x00,0x00,0x00,0x00,0x00},
    // < (0x3C)
    {0x00,0x00,0x01,0x80,0x03,0xC0,0x07,0xE0,0x0C,0x30,0x18,0x18,0x30,0x0C,0x00,0x00},
    // = (0x3D)
    {0x00,0x00,0x06,0x60,0x06,0x60,0x06,0x60,0x06,0x60,0x06,0x60,0x06,0x60,0x00,0x00},
    // > (0x3E)
    {0x00,0x00,0x30,0x0C,0x18,0x18,0x0C,0x30,0x07,0xE0,0x03,0xC0,0x01,0x80,0x00,0x00},
    // ? (0x3F)
    {0x00,0x00,0x00,0x60,0x00,0x70,0x36,0x30,0x37,0x30,0x03,0x30,0x01,0xE0,0x00,0xC0},
    // @ (0x40)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x30,0x30,0x37,0x30,0x37,0x30,0x17,0xE0,0x07,0xC0},
    // A (0x41)
    {0x00,0x00,0x3F,0x00,0x3F,0x80,0x07,0xC0,0x06,0x60,0x07,0xC0,0x3F,0x80,0x3F,0x00},
    // B (0x42)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x33,0x30,0x33,0x30,0x33,0x30,0x1F,0xE0,0x0C,0xC0},
    // C (0x43)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x30,0x30,0x30,0x30,0x30,0x30,0x18,0x60,0x08,0x40},
    // D (0x44)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x30,0x30,0x30,0x30,0x30,0x30,0x1F,0xE0,0x0F,0xC0},
    // E (0x45)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x33,0x30,0x33,0x30,0x33,0x30,0x30,0x30,0x30,0x30},
    // F (0x46)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x03,0x30,0x03,0x30,0x03,0x30,0x00,0x30,0x00,0x30},
    // G (0x47)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x30,0x30,0x33,0x30,0x33,0x30,0x3F,0x60,0x3F,0x40},
    // H (0x48)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x03,0x00,0x03,0x00,0x03,0x00,0x3F,0xF0,0x3F,0xF0},
    // I (0x49)
    {0x00,0x00,0x30,0x30,0x30,0x30,0x3F,0xF0,0x3F,0xF0,0x30,0x30,0x30,0x30,0x00,0x00},
    // J (0x4A)
    {0x00,0x00,0x18,0x00,0x38,0x00,0x30,0x30,0x30,0x30,0x1F,0xF0,0x0F,0xF0,0x00,0x00},
    // K (0x4B)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x03,0x00,0x07,0x80,0x0C,0xC0,0x38,0x70,0x30,0x30},
    // L (0x4C)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00},
    // M (0x4D)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x00,0xE0,0x01,0xC0,0x00,0xE0,0x3F,0xF0,0x3F,0xF0},
    // N (0x4E)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x00,0xE0,0x01,0xC0,0x03,0x80,0x3F,0xF0,0x3F,0xF0},
    // O (0x4F)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x30,0x30,0x30,0x30,0x30,0x30,0x1F,0xE0,0x0F,0xC0},
    // P (0x50)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x03,0x30,0x03,0x30,0x03,0x30,0x01,0xE0,0x00,0xC0},
    // Q (0x51)
    {0x00,0x00,0x0F,0xC0,0x1F,0xE0,0x30,0x30,0x36,0x30,0x3C,0x30,0x5F,0xE0,0x4F,0xC0},
    // R (0x52)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x03,0x30,0x07,0x30,0x0F,0x30,0x39,0xE0,0x30,0xC0},
    // S (0x53)
    {0x00,0x00,0x18,0xC0,0x39,0xE0,0x33,0x30,0x33,0x30,0x33,0x30,0x1E,0x70,0x0C,0x60},
    // T (0x54)
    {0x00,0x00,0x00,0x30,0x00,0x30,0x3F,0xF0,0x3F,0xF0,0x00,0x30,0x00,0x30,0x00,0x00},
    // U (0x55)
    {0x00,0x00,0x1F,0xF0,0x3F,0xF0,0x30,0x00,0x30,0x00,0x30,0x00,0x3F,0xF0,0x1F,0xF0},
    // V (0x56)
    {0x00,0x00,0x07,0xF0,0x1F,0xF0,0x38,0x00,0x30,0x00,0x38,0x00,0x1F,0xF0,0x07,0xF0},
    // W (0x57)
    {0x00,0x00,0x3F,0xF0,0x3F,0xF0,0x1C,0x00,0x0E,0x00,0x1C,0x00,0x3F,0xF0,0x3F,0xF0},
    // X (0x58)
    {0x00,0x00,0x30,0x30,0x38,0x70,0x0F,0xE0,0x07,0xC0,0x0F,0xE0,0x38,0x70,0x30,0x30},
    // Y (0x59)
    {0x00,0x00,0x00,0x70,0x01,0xF0,0x3F,0x80,0x3F,0x80,0x01,0xF0,0x00,0x70,0x00,0x00},
    // Z (0x5A)
    {0x00,0x00,0x38,0x30,0x3C,0x30,0x36,0x30,0x33,0x30,0x31,0xB0,0x30,0xF0,0x30,0x70},
    // [ (0x5B)
    {0x00,0x00,0x00,0x00,0x3F,0xFC,0x3F,0xFC,0x30,0x0C,0x30,0x0C,0x00,0x00,0x00,0x00},
    // \ (0x5C)
    {0x00,0x00,0x01,0x80,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x00,0x00},
    // ] (0x5D)
    {0x00,0x00,0x00,0x00,0x30,0x0C,0x30,0x0C,0x3F,0xFC,0x3F,0xFC,0x00,0x00,0x00,0x00},
    // ^ (0x5E)
    {0x00,0x00,0x02,0x00,0x03,0x00,0x01,0x80,0x00,0xC0,0x01,0x80,0x03,0x00,0x02,0x00},
    // _ (0x5F)
    {0x00,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00},
    // ` (0x60)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x1C,0x00,0x18,0x00,0x00,0x00,0x00},
    // a (0x61)
    {0x00,0x00,0x1C,0x00,0x3E,0x80,0x32,0x80,0x32,0x80,0x32,0x80,0x3F,0x00,0x3F,0x80},
    // b (0x62)
    {0x00,0x00,0x3F,0xF8,0x3F,0xF8,0x31,0x80,0x31,0x80,0x31,0x80,0x1F,0x00,0x0E,0x00},
    // c (0x63)
    {0x00,0x00,0x1F,0x00,0x3F,0x80,0x31,0x80,0x31,0x80,0x31,0x80,0x19,0x80,0x08,0x80},
    // d (0x64)
    {0x00,0x00,0x0E,0x00,0x1F,0x00,0x31,0x80,0x31,0x80,0x31,0x80,0x3F,0xF8,0x3F,0xF8},
    // e (0x65)
    {0x00,0x00,0x1F,0x00,0x3F,0x80,0x35,0x80,0x35,0x80,0x35,0x80,0x1D,0x80,0x0C,0x80},
    // f (0x66)
    {0x00,0x00,0x01,0x80,0x3F,0xF0,0x3F,0xF8,0x01,0x98,0x01,0x98,0x00,0x18,0x00,0x00},
    // g (0x67)
    {0x00,0x00,0x8E,0x00,0xDF,0x00,0xD1,0x80,0xD1,0x80,0xD1,0x80,0xFF,0xF8,0x7F,0xF8},
    // h (0x68)
    {0x00,0x00,0x3F,0xF8,0x3F,0xF8,0x01,0x80,0x01,0x80,0x01,0x80,0x3F,0x00,0x3E,0x00},
    // i (0x69)
    {0x00,0x00,0x00,0x00,0x31,0x80,0x3F,0xD8,0x3F,0xD8,0x30,0x00,0x00,0x00,0x00,0x00},
    // j (0x6A)
    {0x00,0x00,0x80,0x00,0xC0,0x00,0xC1,0x80,0xFF,0xD8,0x7F,0xD8,0x00,0x00,0x00,0x00},
    // k (0x6B)
    {0x00,0x00,0x3F,0xF8,0x3F,0xF8,0x07,0x00,0x0F,0x80,0x39,0xC0,0x30,0x80,0x00,0x00},
    // l (0x6C)
    {0x00,0x00,0x00,0x00,0x30,0x18,0x3F,0xF8,0x3F,0xF8,0x30,0x00,0x00,0x00,0x00,0x00},
    // m (0x6D)
    {0x00,0x00,0x3F,0x80,0x3F,0x80,0x01,0x80,0x3F,0x00,0x01,0x80,0x3F,0x80,0x3E,0x00},
    // n (0x6E)
    {0x00,0x00,0x3F,0x80,0x3F,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x3F,0x00,0x3E,0x00},
    // o (0x6F)
    {0x00,0x00,0x1F,0x00,0x3F,0x80,0x31,0x80,0x31,0x80,0x31,0x80,0x3F,0x80,0x1F,0x00},
    // p (0x70)
    {0x00,0x00,0xFF,0xF8,0xFF,0xF8,0x31,0x80,0x31,0x80,0x31,0x80,0x1F,0x00,0x0E,0x00},
    // q (0x71)
    {0x00,0x00,0x0E,0x00,0x1F,0x00,0x31,0x80,0x31,0x80,0x31,0x80,0xFF,0xF8,0xFF,0xF8},
    // r (0x72)
    {0x00,0x00,0x3F,0x80,0x3F,0x80,0x03,0x00,0x01,0x80,0x01,0x80,0x01,0x80,0x00,0x80},
    // s (0x73)
    {0x00,0x00,0x19,0x80,0x3D,0x80,0x35,0x80,0x35,0x80,0x35,0x80,0x37,0x80,0x26,0x00},
    // t (0x74)
    {0x00,0x00,0x01,0x80,0x1F,0xF0,0x3F,0xF8,0x31,0x80,0x31,0x80,0x30,0x00,0x00,0x00},
    // u (0x75)
    {0x00,0x00,0x1F,0xF8,0x3F,0xF8,0x30,0x00,0x30,0x00,0x30,0x00,0x3F,0xF8,0x3F,0xF8},
    // v (0x76)
    {0x00,0x00,0x07,0xF8,0x1F,0xF8,0x38,0x00,0x30,0x00,0x38,0x00,0x1F,0xF8,0x07,0xF8},
    // w (0x77)
    {0x00,0x00,0x3F,0xF8,0x3F,0xF8,0x1C,0x00,0x0E,0x00,0x1C,0x00,0x3F,0xF8,0x3F,0xF8},
    // x (0x78)
    {0x00,0x00,0x31,0x80,0x3B,0xC0,0x0F,0xE0,0x07,0xC0,0x0F,0xE0,0x3B,0xC0,0x31,0x80},
    // y (0x79)
    {0x00,0x00,0x87,0xF8,0xDF,0xF8,0xD8,0x00,0xD8,0x00,0xD8,0x00,0x7F,0xF8,0x3F,0xF8},
    // z (0x7A)
    {0x00,0x00,0x39,0x80,0x3D,0x80,0x35,0x80,0x33,0x80,0x31,0xC0,0x31,0xE0,0x31,0x70},
    // { (0x7B)
    {0x00,0x00,0x03,0x00,0x03,0x00,0x1F,0xF8,0x3C,0xFC,0x30,0x0C,0x30,0x0C,0x00,0x00},
    // | (0x7C)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xFC,0x3F,0xFC,0x00,0x00,0x00,0x00,0x00,0x00},
    // } (0x7D)
    {0x00,0x00,0x30,0x0C,0x30,0x0C,0x3C,0xFC,0x1F,0xF8,0x03,0x00,0x03,0x00,0x00,0x00},
    // ~ (0x7E)
    {0x00,0x00,0x06,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x06,0x00},
    // DEL (0x7F)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00}
};

#endif /* _HW_FONTS_H_ */
