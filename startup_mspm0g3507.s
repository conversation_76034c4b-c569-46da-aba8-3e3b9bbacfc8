;/*****************************************************************************
; * @file     startup_mspm0g3507.s
; * @brief    CMSIS Cortex-M0+ Core Device Startup File for MSPM0G3507
; * @version  V1.0.0
; * @date     2024-06-29
; ******************************************************************************/

                PRESERVE8
                THUMB

; Vector Table Mapped to Address 0 at Reset
                AREA    RESET, DATA, READONLY
                EXPORT  __Vectors
                EXPORT  __Vectors_End
                EXPORT  __Vectors_Size

__Vectors       DCD     __initial_sp               ; Top of Stack
                DCD     Reset_Handler              ; Reset Handler
                DCD     NMI_Handler                ; NMI Handler
                DCD     HardFault_Handler          ; Hard Fault Handler
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     SVC_Handler                ; SVCall Handler
                DCD     0                          ; Reserved
                DCD     0                          ; Reserved
                DCD     PendSV_Handler             ; PendSV Handler
                DCD     SysTick_Handler            ; SysTick Handler

                ; External Interrupts
                DCD     GROUP0_IRQHandler          ; GROUP0 interrupt
                DCD     GROUP1_IRQHandler          ; GROUP1 interrupt
                DCD     TIMG8_IRQHandler           ; TIMG8 interrupt
                DCD     UART_INST_IRQHandler       ; UART instance interrupt
                DCD     ADC0_IRQHandler            ; ADC0 interrupt
                DCD     ADC1_IRQHandler            ; ADC1 interrupt
                DCD     CANFD_IRQHandler           ; CANFD interrupt
                DCD     DAC_IRQHandler             ; DAC interrupt
                DCD     SPI_INST_IRQHandler        ; SPI instance interrupt
                DCD     I2C_INST_IRQHandler        ; I2C instance interrupt
                DCD     TIMG0_IRQHandler           ; TIMG0 interrupt
                DCD     TIMG6_IRQHandler           ; TIMG6 interrupt
                DCD     TIMA0_IRQHandler           ; TIMA0 interrupt
                DCD     TIMA1_IRQHandler           ; TIMA1 interrupt
                DCD     TIMG7_IRQHandler           ; TIMG7 interrupt
                DCD     TIMG12_IRQHandler          ; TIMG12 interrupt
                DCD     TIMG14_IRQHandler          ; TIMG14 interrupt

__Vectors_End

__Vectors_Size  EQU  __Vectors_End - __Vectors

                AREA    |.text|, CODE, READONLY

; Reset handler
Reset_Handler    PROC
                 EXPORT  Reset_Handler             [WEAK]
                 IMPORT  SystemInit
                 IMPORT  __main
                 LDR     R0, =SystemInit
                 BLX     R0
                 LDR     R0, =__main
                 BX      R0
                 ENDP

; Dummy Exception Handlers (infinite loops which can be modified)

NMI_Handler     PROC
                EXPORT  NMI_Handler                [WEAK]
                B       .
                ENDP
HardFault_Handler\
                PROC
                EXPORT  HardFault_Handler          [WEAK]
                B       .
                ENDP
SVC_Handler     PROC
                EXPORT  SVC_Handler                [WEAK]
                B       .
                ENDP
PendSV_Handler  PROC
                EXPORT  PendSV_Handler             [WEAK]
                B       .
                ENDP
SysTick_Handler PROC
                EXPORT  SysTick_Handler            [WEAK]
                B       .
                ENDP

Default_Handler PROC
                EXPORT  GROUP0_IRQHandler          [WEAK]
                EXPORT  GROUP1_IRQHandler          [WEAK]
                EXPORT  TIMG8_IRQHandler           [WEAK]
                EXPORT  UART_INST_IRQHandler       [WEAK]
                EXPORT  ADC0_IRQHandler            [WEAK]
                EXPORT  ADC1_IRQHandler            [WEAK]
                EXPORT  CANFD_IRQHandler           [WEAK]
                EXPORT  DAC_IRQHandler             [WEAK]
                EXPORT  SPI_INST_IRQHandler        [WEAK]
                EXPORT  I2C_INST_IRQHandler        [WEAK]
                EXPORT  TIMG0_IRQHandler           [WEAK]
                EXPORT  TIMG6_IRQHandler           [WEAK]
                EXPORT  TIMA0_IRQHandler           [WEAK]
                EXPORT  TIMA1_IRQHandler           [WEAK]
                EXPORT  TIMG7_IRQHandler           [WEAK]
                EXPORT  TIMG12_IRQHandler          [WEAK]
                EXPORT  TIMG14_IRQHandler          [WEAK]

GROUP0_IRQHandler
GROUP1_IRQHandler
TIMG8_IRQHandler
UART_INST_IRQHandler
ADC0_IRQHandler
ADC1_IRQHandler
CANFD_IRQHandler
DAC_IRQHandler
SPI_INST_IRQHandler
I2C_INST_IRQHandler
TIMG0_IRQHandler
TIMG6_IRQHandler
TIMA0_IRQHandler
TIMA1_IRQHandler
TIMG7_IRQHandler
TIMG12_IRQHandler
TIMG14_IRQHandler
                B       .

                ENDP

                ALIGN

;*******************************************************************************
; User Stack and Heap initialization
;*******************************************************************************
                 IF      :DEF:__MICROLIB
                
                 EXPORT  __initial_sp
                 EXPORT  __heap_base
                 EXPORT  __heap_limit
                
                 ELSE
                
                 IMPORT  __use_two_region_memory
                 EXPORT  __user_initial_stackheap
                 
__user_initial_stackheap

                 LDR     R0, =  Heap_Mem
                 LDR     R1, =(Stack_Mem + Stack_Size)
                 LDR     R2, = (Heap_Mem +  Heap_Size)
                 LDR     R3, = Stack_Mem
                 BX      LR

                 ALIGN

                 ENDIF

                 END
