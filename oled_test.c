#include "ti_msp_dl_config.h"
#include "hardware/hw_ssd1306.h"

// 简化的OLED测试程序
int main(void)
{
    uint32_t counter = 0;
    uint8_t test_step = 0;

    SYSCFG_DL_init();

    // LED指示系统启动
    DL_GPIO_setPins(DEBUG_LED_PORT, DEBUG_LED_PIN);
    delay_cycles(32000000); // 延时1秒 - LED长亮表示系统启动
    DL_GPIO_clearPins(DEBUG_LED_PORT, DEBUG_LED_PIN);
    
    // 初始化OLED
    oled_init();
    
    // LED快速闪烁表示OLED初始化完成
    for(uint8_t i = 0; i < 6; i++) {
        DL_GPIO_setPins(DEBUG_LED_PORT, DEBUG_LED_PIN);
        delay_cycles(3200000); // 0.1秒
        DL_GPIO_clearPins(DEBUG_LED_PORT, DEBUG_LED_PIN);
        delay_cycles(3200000); // 0.1秒
    }
   
    while (1) 
    {
        switch(test_step) {
            case 0:
                // 测试1: 全屏点亮
                for(uint8_t x = 0; x < 128; x++) {
                    for(uint8_t y = 0; y < 64; y++) {
                        oled_set_pixel(x, y, OLED_COLOR_WHITE);
                    }
                }
                oled_update_display();
                break;
                
            case 1:
                // 测试2: 清屏
                oled_clear();
                oled_update_display();
                break;
                
            case 2:
                // 测试3: 显示文字
                oled_clear();
                oled_draw_string(0, 0, "OLED Test");
                oled_draw_string(0, 16, "Hello World");
                oled_draw_string(0, 32, "Line 3");
                oled_draw_string(0, 48, "Line 4");
                oled_update_display();
                break;
                
            case 3:
                // 测试4: 显示数字
                oled_clear();
                oled_draw_string(0, 0, "Numbers:");
                oled_draw_number(0, 16, 123.4, 1);
                oled_draw_number(0, 32, -56.7, 1);
                oled_draw_number(0, 48, 890.1, 1);
                oled_update_display();
                break;
                
            case 4:
                // 测试5: 边框测试
                oled_clear();
                // 画边框
                for(uint8_t x = 0; x < 128; x++) {
                    oled_set_pixel(x, 0, OLED_COLOR_WHITE);
                    oled_set_pixel(x, 63, OLED_COLOR_WHITE);
                }
                for(uint8_t y = 0; y < 64; y++) {
                    oled_set_pixel(0, y, OLED_COLOR_WHITE);
                    oled_set_pixel(127, y, OLED_COLOR_WHITE);
                }
                oled_draw_string(20, 24, "Border Test");
                oled_update_display();
                break;
        }
        
        // LED心跳指示
        if(counter % 10 == 0) {
            DL_GPIO_setPins(DEBUG_LED_PORT, DEBUG_LED_PIN);
            delay_cycles(1600000); // 短暂点亮
            DL_GPIO_clearPins(DEBUG_LED_PORT, DEBUG_LED_PIN);
        }
        
        counter++;
        
        // 每3秒切换测试
        if(counter >= 30) {
            test_step++;
            if(test_step > 4) test_step = 0;
            counter = 0;
        }
        
        // 延时约100ms
        delay_cycles(3200000);
    }
}
