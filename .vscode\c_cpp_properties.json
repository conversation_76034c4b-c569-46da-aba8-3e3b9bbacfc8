{"configurations": [{"name": "Target 1", "includePath": ["d:\\xiazai\\electric-racing-car-master (1)\\electric-racing-car-master\\examples\\jy901s\\hardware", "D:\\Keil\\ARM\\ARMCLANG\\include", "D:\\Keil\\ARM\\ARMCLANG\\include\\arm_linux", "D:\\Keil\\ARM\\ARMCLANG\\include\\arm_linux_compat", "D:\\Keil\\ARM\\ARMCLANG\\include\\libcxx", "d:\\xiazai\\electric-racing-car-master (1)\\electric-racing-car-master\\examples\\jy901s"], "defines": ["__MSPM0G3507__", "__alignof__(x)=", "__asm(x)=", "__asm__(x)=", "__forceinline=", "__restrict=", "__volatile__=", "__inline=", "__inline__=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__unaligned=", "__promise(x)=", "__irq=", "__swi=", "__weak=", "__register=", "__pure=", "__value_in_regs=", "__breakpoint(x)=", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__enable_fiq()=", "__enable_irq()=", "__force_stores()=", "__memory_changed()=", "__schedule_barrier()=", "__semihost(x,y)=0", "__vfp_status(x,y)=0", "__builtin_arm_nop()=", "__builtin_arm_wfi()=", "__builtin_arm_wfe()=", "__builtin_arm_sev()=", "__builtin_arm_sevl()=", "__builtin_arm_yield()=", "__builtin_arm_isb(x)=", "__builtin_arm_dsb(x)=", "__builtin_arm_dmb(x)=", "__builtin_bswap32(x)=0U", "__builtin_bswap16(x)=0U", "__builtin_arm_rbit(x)=0U", "__builtin_clz(x)=0U", "__builtin_arm_ldrex(x)=0U", "__builtin_arm_strex(x,y)=0U", "__builtin_arm_clrex()=", "__builtin_arm_ssat(x,y)=0U", "__builtin_arm_usat(x,y)=0U", "__builtin_arm_ldaex(x)=0U", "__builtin_arm_stlex(x,y)=0U", "_ILP32=1", "_USE_STATIC_INLINE=1", "__APCS_32__=1", "__ARMCC_VERSION=6220000", "__ARMCOMPILER_VERSION=6220000", "__ARMEL__=1", "__ARM_32BIT_STATE=1", "__ARM_ACLE=200", "__ARM_ARCH=4", "__ARM_ARCH_4T__=1", "__ARM_ARCH_ISA_ARM=1", "__ARM_ARCH_ISA_THUMB=1", "__ARM_EABI__=1", "__ARM_FEATURE_COPROC=0x1", "__ARM_FP16_ARGS=1", "__ARM_FP16_FORMAT_IEEE=1", "__ARM_NO_IMAGINARY_TYPE=1", "__ARM_PCS=1", "__ARM_PROMISE=__builtin_assume", "__ARM_SIZEOF_MINIMAL_ENUM=4", "__ARM_SIZEOF_WCHAR_T=4", "__ATOMIC_ACQUIRE=2", "__ATOMIC_ACQ_REL=4", "__ATOMIC_CONSUME=1", "__ATOMIC_RELAXED=0", "__ATOMIC_RELEASE=3", "__ATOMIC_SEQ_CST=5", "__BIGGEST_ALIGNMENT__=8", "__BITINT_MAXWIDTH__=128", "__BOOL_WIDTH__=8", "__BYTE_ORDER__=__ORDER_LITTLE_ENDIAN__", "__CHAR16_TYPE__=unsigned short", "__CHAR32_TYPE__=unsigned int", "__CHAR_BIT__=8", "__CHAR_UNSIGNED__=1", "__CLANG_ATOMIC_BOOL_LOCK_FREE=1", "__CLANG_ATOMIC_CHAR16_T_LOCK_FREE=1", "__CLANG_ATOMIC_CHAR32_T_LOCK_FREE=1", "__CLANG_ATOMIC_CHAR_LOCK_FREE=1", "__CLANG_ATOMIC_INT_LOCK_FREE=1", "__CLANG_ATOMIC_LLONG_LOCK_FREE=1", "__CLANG_ATOMIC_LONG_LOCK_FREE=1", "__CLANG_ATOMIC_POINTER_LOCK_FREE=1", "__CLANG_ATOMIC_SHORT_LOCK_FREE=1", "__CLANG_ATOMIC_WCHAR_T_LOCK_FREE=1", "__CONSTANT_CFSTRINGS__=1", "__DBL_DECIMAL_DIG__=17", "__DBL_DENORM_MIN__=4.9406564584124654e-324", "__DBL_DIG__=15", "__DBL_EPSILON__=2.2204460492503131e-16", "__DBL_HAS_DENORM__=1", "__DBL_HAS_INFINITY__=1", "__DBL_HAS_QUIET_NAN__=1", "__DBL_MANT_DIG__=53", "__DBL_MAX_10_EXP__=308", "__DBL_MAX_EXP__=1024", "__DBL_MAX__=1.7976931348623157e+308", "__DBL_MIN_10_EXP__=(-307)", "__DBL_MIN_EXP__=(-1021)", "__DBL_MIN__=2.2250738585072014e-308", "__DECIMAL_DIG__=__LDBL_DECIMAL_DIG__", "__ELF__=1", "__ESCAPE__=", "__FINITE_MATH_ONLY__=1", "__FLT16_DECIMAL_DIG__=5", "__FLT16_DENORM_MIN__=5.9604644775390625e-8F16", "__FLT16_DIG__=3", "__FLT16_EPSILON__=9.765625e-4F16", "__FLT16_HAS_DENORM__=1", "__FLT16_HAS_INFINITY__=1", "__FLT16_HAS_QUIET_NAN__=1", "__FLT16_MANT_DIG__=11", "__FLT16_MAX_10_EXP__=4", "__FLT16_MAX_EXP__=16", "__FLT16_MAX__=6.5504e+4F16", "__FLT16_MIN_10_EXP__=(-4)", "__FLT16_MIN_EXP__=(-13)", "__FLT16_MIN__=6.103515625e-5F16", "__FLT_DECIMAL_DIG__=9", "__FLT_DENORM_MIN__=1.40129846e-45F", "__FLT_DIG__=6", "__FLT_EPSILON__=1.19209290e-7F", "__FLT_HAS_DENORM__=1", "__FLT_HAS_INFINITY__=1", "__FLT_HAS_QUIET_NAN__=1", "__FLT_MANT_DIG__=24", "__FLT_MAX_10_EXP__=38", "__FLT_MAX_EXP__=128", "__FLT_MAX__=3.40282347e+38F", "__FLT_MIN_10_EXP__=(-37)", "__FLT_MIN_EXP__=(-125)", "__FLT_MIN__=1.17549435e-38F", "__FLT_RADIX__=2", "__FPCLASS_NEGINF=0x0004", "__FPCLASS_NEGNORMAL=0x0008", "__FPCLASS_NEGSUBNORMAL=0x0010", "__FPCLASS_NEGZERO=0x0020", "__FPCLASS_POSINF=0x0200", "__FPCLASS_POSNORMAL=0x0100", "__FPCLASS_POSSUBNORMAL=0x0080", "__FPCLASS_POSZERO=0x0040", "__FPCLASS_QNAN=0x0002", "__FPCLASS_SNAN=0x0001", "__GCC_ATOMIC_BOOL_LOCK_FREE=1", "__GCC_ATOMIC_CHAR16_T_LOCK_FREE=1", "__GCC_ATOMIC_CHAR32_T_LOCK_FREE=1", "__GCC_ATOMIC_CHAR_LOCK_FREE=1", "__GCC_ATOMIC_INT_LOCK_FREE=1", "__GCC_ATOMIC_LLONG_LOCK_FREE=1", "__GCC_ATOMIC_LONG_LOCK_FREE=1", "__GCC_ATOMIC_POINTER_LOCK_FREE=1", "__GCC_ATOMIC_SHORT_LOCK_FREE=1", "__GCC_ATOMIC_TEST_AND_SET_TRUEVAL=1", "__GCC_ATOMIC_WCHAR_T_LOCK_FREE=1", "__GNUC_MINOR__=2", "__GNUC_PATCHLEVEL__=1", "__GNUC_STDC_INLINE__=1", "__GNUC__=4", "__GXX_ABI_VERSION=1002", "__ILP32__=1", "__INT16_C_SUFFIX__=", "__INT16_FMTd__=\"hd\"", "__INT16_FMTi__=\"hi\"", "__INT16_MAX__=32767", "__INT16_TYPE__=short", "__INT32_C_SUFFIX__=", "__INT32_FMTd__=\"d\"", "__INT32_FMTi__=\"i\"", "__INT32_MAX__=2147483647", "__INT32_TYPE__=int", "__INT64_C_SUFFIX__=LL", "__INT64_FMTd__=\"lld\"", "__INT64_FMTi__=\"lli\"", "__INT64_MAX__=9223372036854775807LL", "__INT64_TYPE__=long long int", "__INT8_C_SUFFIX__=", "__INT8_FMTd__=\"hhd\"", "__INT8_FMTi__=\"hhi\"", "__INT8_MAX__=127", "__INT8_TYPE__=signed char", "__INTMAX_C_SUFFIX__=LL", "__INTMAX_FMTd__=\"lld\"", "__INTMAX_FMTi__=\"lli\"", "__INTMAX_MAX__=9223372036854775807LL", "__INTMAX_TYPE__=long long int", "__INTMAX_WIDTH__=64", "__INTPTR_FMTd__=\"d\"", "__INTPTR_FMTi__=\"i\"", "__INTPTR_MAX__=2147483647", "__INTPTR_TYPE__=int", "__INTPTR_WIDTH__=32", "__INT_FAST16_FMTd__=\"hd\"", "__INT_FAST16_FMTi__=\"hi\"", "__INT_FAST16_MAX__=32767", "__INT_FAST16_TYPE__=short", "__INT_FAST16_WIDTH__=16", "__INT_FAST32_FMTd__=\"d\"", "__INT_FAST32_FMTi__=\"i\"", "__INT_FAST32_MAX__=2147483647", "__INT_FAST32_TYPE__=int", "__INT_FAST32_WIDTH__=32", "__INT_FAST64_FMTd__=\"lld\"", "__INT_FAST64_FMTi__=\"lli\"", "__INT_FAST64_MAX__=9223372036854775807LL", "__INT_FAST64_TYPE__=long long int", "__INT_FAST64_WIDTH__=64", "__INT_FAST8_FMTd__=\"hhd\"", "__INT_FAST8_FMTi__=\"hhi\"", "__INT_FAST8_MAX__=127", "__INT_FAST8_TYPE__=signed char", "__INT_FAST8_WIDTH__=8", "__INT_LEAST16_FMTd__=\"hd\"", "__INT_LEAST16_FMTi__=\"hi\"", "__INT_LEAST16_MAX__=32767", "__INT_LEAST16_TYPE__=short", "__INT_LEAST16_WIDTH__=16", "__INT_LEAST32_FMTd__=\"d\"", "__INT_LEAST32_FMTi__=\"i\"", "__INT_LEAST32_MAX__=2147483647", "__INT_LEAST32_TYPE__=int", "__INT_LEAST32_WIDTH__=32", "__INT_LEAST64_FMTd__=\"lld\"", "__INT_LEAST64_FMTi__=\"lli\"", "__INT_LEAST64_MAX__=9223372036854775807LL", "__INT_LEAST64_TYPE__=long long int", "__INT_LEAST64_WIDTH__=64", "__INT_LEAST8_FMTd__=\"hhd\"", "__INT_LEAST8_FMTi__=\"hhi\"", "__INT_LEAST8_MAX__=127", "__INT_LEAST8_TYPE__=signed char", "__INT_LEAST8_WIDTH__=8", "__INT_MAX__=2147483647", "__INT_WIDTH__=32", "__I__=1.0if", "__LDBL_DECIMAL_DIG__=17", "__LDBL_DENORM_MIN__=4.9406564584124654e-324L", "__LDBL_DIG__=15", "__LDBL_EPSILON__=2.2204460492503131e-16L", "__LDBL_HAS_DENORM__=1", "__LDBL_HAS_INFINITY__=1", "__LDBL_HAS_QUIET_NAN__=1", "__LDBL_MANT_DIG__=53", "__LDBL_MAX_10_EXP__=308", "__LDBL_MAX_EXP__=1024", "__LDBL_MAX__=1.7976931348623157e+308L", "__LDBL_MIN_10_EXP__=(-307)", "__LDBL_MIN_EXP__=(-1021)", "__LDBL_MIN__=2.2250738585072014e-308L", "__LITTLE_ENDIAN__=1", "__LLONG_WIDTH__=64", "__LONG_LONG_MAX__=9223372036854775807LL", "__LONG_MAX__=2147483647L", "__LONG_WIDTH__=32", "__MEMORY_SCOPE_DEVICE=1", "__MEMORY_SCOPE_SINGLE=4", "__MEMORY_SCOPE_SYSTEM=0", "__MEMORY_SCOPE_WRKGRP=2", "__MEMORY_SCOPE_WVFRNT=3", "__NO_INLINE__=1", "__OBJC_BOOL_IS_BOOL=0", "__OPENCL_MEMORY_SCOPE_ALL_SVM_DEVICES=3", "__OPENCL_MEMORY_SCOPE_DEVICE=2", "__OPENCL_MEMORY_SCOPE_SUB_GROUP=4", "__OPENCL_MEMORY_SCOPE_WORK_GROUP=1", "__OPENCL_MEMORY_SCOPE_WORK_ITEM=0", "__ORDER_BIG_ENDIAN__=4321", "__ORDER_LITTLE_ENDIAN__=1234", "__ORDER_PDP_ENDIAN__=3412", "__POINTER_WIDTH__=32", "__PRAGMA_REDEFINE_EXTNAME=1", "__PTRDIFF_FMTd__=\"d\"", "__PTRDIFF_FMTi__=\"i\"", "__PTRDIFF_MAX__=2147483647", "__PTRDIFF_TYPE__=int", "__PTRDIFF_WIDTH__=32", "__REGISTER_PREFIX__=", "__SCHAR_MAX__=127", "__SHRT_MAX__=32767", "__SHRT_WIDTH__=16", "__SIG_ATOMIC_MAX__=2147483647", "__SIG_ATOMIC_WIDTH__=32", "__SIZEOF_DOUBLE__=8", "__SIZEOF_FLOAT__=4", "__SIZEOF_INT__=4", "__SIZEOF_LONG_DOUBLE__=8", "__SIZEOF_LONG_LONG__=8", "__SIZEOF_LONG__=4", "__SIZEOF_POINTER__=4", "__SIZEOF_PTRDIFF_T__=4", "__SIZEOF_SHORT__=2", "__SIZEOF_SIZE_T__=4", "__SIZEOF_WCHAR_T__=4", "__SIZEOF_WINT_T__=4", "__SIZE_FMTX__=\"X\"", "__SIZE_FMTo__=\"o\"", "__SIZE_FMTu__=\"u\"", "__SIZE_FMTx__=\"x\"", "__SIZE_MAX__=4294967295U", "__SIZE_TYPE__=unsigned int", "__SIZE_WIDTH__=32", "__SOFTFP__=1", "__STDC_HOSTED__=1", "__STDC_UTF_16__=1", "__STDC_UTF_32__=1", "__STDC_VERSION__=201710L", "__STDC__=1", "__UINT16_C_SUFFIX__=", "__UINT16_FMTX__=\"hX\"", "__UINT16_FMTo__=\"ho\"", "__UINT16_FMTu__=\"hu\"", "__UINT16_FMTx__=\"hx\"", "__UINT16_MAX__=65535", "__UINT16_TYPE__=unsigned short", "__UINT32_C_SUFFIX__=U", "__UINT32_FMTX__=\"X\"", "__UINT32_FMTo__=\"o\"", "__UINT32_FMTu__=\"u\"", "__UINT32_FMTx__=\"x\"", "__UINT32_MAX__=4294967295U", "__UINT32_TYPE__=unsigned int", "__UINT64_C_SUFFIX__=ULL", "__UINT64_FMTX__=\"llX\"", "__UINT64_FMTo__=\"llo\"", "__UINT64_FMTu__=\"llu\"", "__UINT64_FMTx__=\"llx\"", "__UINT64_MAX__=18446744073709551615ULL", "__UINT64_TYPE__=long long unsigned int", "__UINT8_C_SUFFIX__=", "__UINT8_FMTX__=\"hhX\"", "__UINT8_FMTo__=\"hho\"", "__UINT8_FMTu__=\"hhu\"", "__UINT8_FMTx__=\"hhx\"", "__UINT8_MAX__=255", "__UINT8_TYPE__=unsigned char", "__UINTMAX_C_SUFFIX__=ULL", "__UINTMAX_FMTX__=\"llX\"", "__UINTMAX_FMTo__=\"llo\"", "__UINTMAX_FMTu__=\"llu\"", "__UINTMAX_FMTx__=\"llx\"", "__UINTMAX_MAX__=18446744073709551615ULL", "__UINTMAX_TYPE__=long long unsigned int", "__UINTMAX_WIDTH__=64", "__UINTPTR_FMTX__=\"X\"", "__UINTPTR_FMTo__=\"o\"", "__UINTPTR_FMTu__=\"u\"", "__UINTPTR_FMTx__=\"x\"", "__UINTPTR_MAX__=4294967295U", "__UINTPTR_TYPE__=unsigned int", "__UINTPTR_WIDTH__=32", "__UINT_FAST16_FMTX__=\"hX\"", "__UINT_FAST16_FMTo__=\"ho\"", "__UINT_FAST16_FMTu__=\"hu\"", "__UINT_FAST16_FMTx__=\"hx\"", "__UINT_FAST16_MAX__=65535", "__UINT_FAST16_TYPE__=unsigned short", "__UINT_FAST32_FMTX__=\"X\"", "__UINT_FAST32_FMTo__=\"o\"", "__UINT_FAST32_FMTu__=\"u\"", "__UINT_FAST32_FMTx__=\"x\"", "__UINT_FAST32_MAX__=4294967295U", "__UINT_FAST32_TYPE__=unsigned int", "__UINT_FAST64_FMTX__=\"llX\"", "__UINT_FAST64_FMTo__=\"llo\"", "__UINT_FAST64_FMTu__=\"llu\"", "__UINT_FAST64_FMTx__=\"llx\"", "__UINT_FAST64_MAX__=18446744073709551615ULL", "__UINT_FAST64_TYPE__=long long unsigned int", "__UINT_FAST8_FMTX__=\"hhX\"", "__UINT_FAST8_FMTo__=\"hho\"", "__UINT_FAST8_FMTu__=\"hhu\"", "__UINT_FAST8_FMTx__=\"hhx\"", "__UINT_FAST8_MAX__=255", "__UINT_FAST8_TYPE__=unsigned char", "__UINT_LEAST16_FMTX__=\"hX\"", "__UINT_LEAST16_FMTo__=\"ho\"", "__UINT_LEAST16_FMTu__=\"hu\"", "__UINT_LEAST16_FMTx__=\"hx\"", "__UINT_LEAST16_MAX__=65535", "__UINT_LEAST16_TYPE__=unsigned short", "__UINT_LEAST32_FMTX__=\"X\"", "__UINT_LEAST32_FMTo__=\"o\"", "__UINT_LEAST32_FMTu__=\"u\"", "__UINT_LEAST32_FMTx__=\"x\"", "__UINT_LEAST32_MAX__=4294967295U", "__UINT_LEAST32_TYPE__=unsigned int", "__UINT_LEAST64_FMTX__=\"llX\"", "__UINT_LEAST64_FMTo__=\"llo\"", "__UINT_LEAST64_FMTu__=\"llu\"", "__UINT_LEAST64_FMTx__=\"llx\"", "__UINT_LEAST64_MAX__=18446744073709551615ULL", "__UINT_LEAST64_TYPE__=long long unsigned int", "__UINT_LEAST8_FMTX__=\"hhX\"", "__UINT_LEAST8_FMTo__=\"hho\"", "__UINT_LEAST8_FMTu__=\"hhu\"", "__UINT_LEAST8_FMTx__=\"hhx\"", "__UINT_LEAST8_MAX__=255", "__UINT_LEAST8_TYPE__=unsigned char", "__USER_LABEL_PREFIX__=", "__VERSION__=\"Clang 19.0.0git\"", "__VFP_FP__=1", "__WCHAR_MAX__=4294967295U", "__WCHAR_TYPE__=unsigned int", "__WCHAR_UNSIGNED__=1", "__WCHAR_WIDTH__=32", "__WINT_MAX__=2147483647", "__WINT_TYPE__=int", "__WINT_WIDTH__=32", "__arm=1", "__arm__=1", "__clang__=1", "__clang_literal_encoding__=\"UTF-8\"", "__clang_major__=19", "__clang_minor__=0", "__clang_patchlevel__=0", "__clang_version__=\"19.0.0git \"", "__clang_wide_literal_encoding__=\"UTF-32\"", "__llvm__=1"], "intelliSenseMode": "${default}"}], "version": 4}