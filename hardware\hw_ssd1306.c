#include "hw_ssd1306.h"
#include "hw_fonts.h"
#include "string.h"

// OLED显示缓冲区
static uint8_t oled_buffer[SSD1306_BUFFER_SIZE];

// OLED I2C通信函数（复用JY901S的I2C实现模式）
static void oled_i2c_start(void)
{
    OLED_SDA_OUT();
    OLED_SDA(1);
    OLED_SCL(1);
    delay_us(4);
    OLED_SDA(0);
    delay_us(4);
    OLED_SCL(0);
}

static void oled_i2c_stop(void)
{
    OLED_SDA_OUT();
    OLED_SCL(0);
    OLED_SDA(0);
    delay_us(4);
    OLED_SCL(1);
    delay_us(4);
    OLED_SDA(1);
    delay_us(4);
}

static uint8_t oled_i2c_wait_ack(void)
{
    uint8_t ucErrTime = 0;
    OLED_SDA_IN();
    OLED_SDA(1);
    delay_us(1);
    OLED_SCL(1);
    delay_us(1);
    while(OLED_SDA_GET()) {
        ucErrTime++;
        if(ucErrTime > 250) {
            oled_i2c_stop();
            return 1;
        }
    }
    OLED_SCL(0);
    return 0;
}

static void oled_i2c_ack(void)
{
    OLED_SCL(0);
    OLED_SDA_OUT();
    OLED_SDA(0);
    delay_us(2);
    OLED_SCL(1);
    delay_us(2);
    OLED_SCL(0);
}

static void oled_i2c_nack(void)
{
    OLED_SCL(0);
    OLED_SDA_OUT();
    OLED_SDA(1);
    delay_us(2);
    OLED_SCL(1);
    delay_us(2);
    OLED_SCL(0);
}

static void oled_i2c_send_byte(uint8_t txd)
{
    uint8_t t;
    OLED_SDA_OUT();
    OLED_SCL(0);
    for(t = 0; t < 8; t++) {
        OLED_SDA((txd & 0x80) >> 7);
        txd <<= 1;
        delay_us(2);
        OLED_SCL(1);
        delay_us(2);
        OLED_SCL(0);
        delay_us(2);
    }
}

// OLED写命令
void oled_write_cmd(uint8_t cmd)
{
    oled_i2c_start();
    oled_i2c_send_byte((SSD1306_I2C_ADDRESS << 1) | 0); // 写地址
    oled_i2c_wait_ack();
    oled_i2c_send_byte(0x00); // 命令模式
    oled_i2c_wait_ack();
    oled_i2c_send_byte(cmd);
    oled_i2c_wait_ack();
    oled_i2c_stop();
}

// OLED写数据
void oled_write_data(uint8_t data)
{
    oled_i2c_start();
    oled_i2c_send_byte((SSD1306_I2C_ADDRESS << 1) | 0); // 写地址
    oled_i2c_wait_ack();
    oled_i2c_send_byte(0x40); // 数据模式
    oled_i2c_wait_ack();
    oled_i2c_send_byte(data);
    oled_i2c_wait_ack();
    oled_i2c_stop();
}

// OLED初始化
void oled_init(void)
{
    delay_ms(100); // 等待OLED稳定
    
    // 初始化序列
    oled_write_cmd(SSD1306_DISPLAYOFF);         // 关闭显示
    oled_write_cmd(SSD1306_SETDISPLAYCLOCKDIV); // 设置时钟分频
    oled_write_cmd(0x80);
    oled_write_cmd(SSD1306_SETMULTIPLEX);       // 设置复用率
    oled_write_cmd(SSD1306_HEIGHT - 1);
    oled_write_cmd(SSD1306_SETDISPLAYOFFSET);   // 设置显示偏移
    oled_write_cmd(0x00);
    oled_write_cmd(SSD1306_SETSTARTLINE | 0x0); // 设置起始行
    oled_write_cmd(SSD1306_CHARGEPUMP);         // 设置电荷泵
    oled_write_cmd(0x14);
    oled_write_cmd(SSD1306_MEMORYMODE);         // 设置内存模式
    oled_write_cmd(0x00);
    oled_write_cmd(SSD1306_SEGREMAP | 0x1);     // 设置段重映射
    oled_write_cmd(SSD1306_COMSCANDEC);         // 设置COM扫描方向
    oled_write_cmd(SSD1306_SETCOMPINS);         // 设置COM引脚配置
    oled_write_cmd(0x12);
    oled_write_cmd(SSD1306_SETCONTRAST);        // 设置对比度
    oled_write_cmd(0xCF);
    oled_write_cmd(SSD1306_SETPRECHARGE);       // 设置预充电周期
    oled_write_cmd(0xF1);
    oled_write_cmd(SSD1306_SETVCOMDETECT);      // 设置VCOM检测
    oled_write_cmd(0x40);
    oled_write_cmd(SSD1306_DISPLAYALLON_RESUME); // 恢复显示
    oled_write_cmd(SSD1306_NORMALDISPLAY);      // 正常显示
    oled_write_cmd(SSD1306_DISPLAYON);          // 开启显示
    
    oled_clear(); // 清屏
    oled_update_display(); // 刷新显示
}

// 清空显示缓冲区
void oled_clear(void)
{
    memset(oled_buffer, 0, SSD1306_BUFFER_SIZE);
}

// 设置像素
void oled_set_pixel(uint8_t x, uint8_t y, uint8_t color)
{
    if(x >= SSD1306_WIDTH || y >= SSD1306_HEIGHT) return;
    
    if(color) {
        oled_buffer[x + (y / 8) * SSD1306_WIDTH] |= (1 << (y % 8));
    } else {
        oled_buffer[x + (y / 8) * SSD1306_WIDTH] &= ~(1 << (y % 8));
    }
}

// 刷新显示
void oled_update_display(void)
{
    uint16_t i;
    
    oled_write_cmd(SSD1306_COLUMNADDR);
    oled_write_cmd(0);   // 列起始地址
    oled_write_cmd(SSD1306_WIDTH - 1); // 列结束地址
    
    oled_write_cmd(SSD1306_PAGEADDR);
    oled_write_cmd(0);   // 页起始地址
    oled_write_cmd((SSD1306_HEIGHT / 8) - 1); // 页结束地址
    
    // 发送显示数据
    for(i = 0; i < SSD1306_BUFFER_SIZE; i++) {
        oled_write_data(oled_buffer[i]);
    }
}

// 显示单个字符
void oled_draw_char(uint8_t x, uint8_t y, char ch)
{
    uint8_t i, j;
    uint8_t char_index;

    if(x >= SSD1306_WIDTH || y >= SSD1306_HEIGHT) return;
    if(ch < 0x20 || ch > 0x7F) ch = 0x20; // 超出范围显示空格

    char_index = ch - 0x20; // 计算字符在字体数组中的索引

    for(i = 0; i < FONT_WIDTH; i++) {
        uint16_t font_data = (font8x16[char_index][i*2] << 8) | font8x16[char_index][i*2+1];
        for(j = 0; j < FONT_HEIGHT; j++) {
            if(font_data & (1 << j)) {
                oled_set_pixel(x + i, y + j, OLED_COLOR_WHITE);
            }
        }
    }
}

// 显示字符串
void oled_draw_string(uint8_t x, uint8_t y, const char* str)
{
    uint8_t pos_x = x;

    while(*str) {
        if(pos_x + FONT_WIDTH > SSD1306_WIDTH) break; // 超出屏幕宽度
        oled_draw_char(pos_x, y, *str);
        pos_x += FONT_WIDTH;
        str++;
    }
}

// 显示浮点数
void oled_draw_number(uint8_t x, uint8_t y, float num, uint8_t decimal)
{
    char str[16];
    int16_t int_part = (int16_t)num;
    uint16_t frac_part;
    uint8_t i = 0;

    // 处理负数
    if(num < 0) {
        str[i++] = '-';
        num = -num;
        int_part = -int_part;
    }

    // 整数部分
    if(int_part == 0) {
        str[i++] = '0';
    } else {
        char temp[8];
        uint8_t j = 0;
        while(int_part > 0) {
            temp[j++] = '0' + (int_part % 10);
            int_part /= 10;
        }
        while(j > 0) {
            str[i++] = temp[--j];
        }
    }

    // 小数部分
    if(decimal > 0) {
        str[i++] = '.';
        frac_part = (uint16_t)((num - (int16_t)num) * 10);
        str[i++] = '0' + frac_part;
    }

    str[i] = '\0';
    oled_draw_string(x, y, str);
}

// 显示姿态数据
void oled_display_attitude(GYRO_ANGLE_DATA_STRUCT* data)
{
    oled_clear();

    // 标题
    oled_draw_string(20, 0, "Attitude Data");

    // Roll角度
    oled_draw_string(0, 16, "Roll:");
    oled_draw_number(48, 16, data->x, 1);
    oled_draw_string(88, 16, "deg");

    // Pitch角度
    oled_draw_string(0, 32, "Pitch:");
    oled_draw_number(48, 32, data->y, 1);
    oled_draw_string(88, 32, "deg");

    // Yaw角度
    oled_draw_string(0, 48, "Yaw:");
    oled_draw_number(48, 48, data->z, 1);
    oled_draw_string(88, 48, "deg");

    oled_update_display();
}
