<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\jy901s.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\jy901s.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6220000: Last Updated: Mon Jun 30 13:32:36 2025
<BR><P>
<H3>Maximum Stack Usage =        308 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; oled_display_attitude &rArr; oled_draw_number &rArr; oled_draw_string &rArr; oled_draw_char &rArr; oled_set_pixel
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
 <LI><a href="#[5]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">SysTick_Handler</a><BR>
 <LI><a href="#[a]">ADC0_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">ADC0_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[c]">CANFD_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[d]">DAC_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[f]">I2C_INST_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[e]">SPI_INST_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[18]">SystemInit</a> from system_mspm0g3507.o(.text.SystemInit) referenced from startup_mspm0g3507.o(.text)
 <LI><a href="#[12]">TIMA0_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[13]">TIMA1_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[10]">TIMG0_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[15]">TIMG12_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[16]">TIMG14_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[11]">TIMG6_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[14]">TIMG7_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[9]">UART_INST_IRQHandler</a> from startup_mspm0g3507.o(.text) referenced from startup_mspm0g3507.o(RESET)
 <LI><a href="#[19]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_mspm0g3507.o(.text)
 <LI><a href="#[17]">main</a> from empty.o(.text.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[19]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(.text)
</UL>
<P><STRONG><a name="[6d]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[1a]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[34]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[6e]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[6f]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[70]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[71]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[72]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>I2C_INST_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI_INST_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMG14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART_INST_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g3507.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[73]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[1c]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_clear
</UL>

<P><STRONG><a name="[75]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[76]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[1e]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[1f]"></a>__aeabi_fadd</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_frsub
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
</UL>

<P><STRONG><a name="[22]"></a>__aeabi_fsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = __aeabi_fsub &rArr; __aeabi_fadd &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[23]"></a>__aeabi_frsub</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>

<P><STRONG><a name="[60]"></a>__aeabi_fmul</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, fmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[24]"></a>__aeabi_dadd</STRONG> (Thumb, 328 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[29]"></a>__aeabi_dsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[2a]"></a>__aeabi_drsub</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[2b]"></a>__aeabi_dmul</STRONG> (Thumb, 202 bytes, Stack size 72 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[2c]"></a>__aeabi_ddiv</STRONG> (Thumb, 234 bytes, Stack size 40 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[5d]"></a>__aeabi_fcmpge</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, fcmpge.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[43]"></a>__aeabi_dcmple</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, dcmple.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_dcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[44]"></a>__aeabi_dcmpge</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, dcmpge.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[2d]"></a>__aeabi_i2f</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_i2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[2e]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[5e]"></a>__aeabi_f2iz</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, ffixi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[42]"></a>__aeabi_f2d</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[2f]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_uidiv$div0</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, uidiv_div0.o(.text), UNUSED)

<P><STRONG><a name="[31]"></a>__aeabi_uidivmod</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, uidiv_div0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[5f]"></a>__aeabi_idiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, idiv_div0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_idiv
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[30]"></a>__aeabi_idivmod</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, idiv_div0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_idivmod &rArr; __aeabi_uidivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
</UL>

<P><STRONG><a name="[25]"></a>__aeabi_llsl</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, llshl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[78]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[26]"></a>__aeabi_lasr</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, llsshr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_lasr
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[79]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[7a]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[21]"></a>_float_round</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[20]"></a>_float_epilogue</STRONG> (Thumb, 114 bytes, Stack size 12 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>

<P><STRONG><a name="[28]"></a>_double_round</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>

<P><STRONG><a name="[27]"></a>_double_epilogue</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_clz
<LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[1b]"></a>__scatterload</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[7b]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[33]"></a>__aeabi_llsr</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, llushr.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[7c]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[35]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 144 bytes, Stack size 40 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SYSCFG_DL_GPIO_init &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[3c]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[3a]"></a>SYSCFG_DL_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_GPIO_init &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3b]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SYSCFG_DL_initPower &rArr; DL_GPIO_enablePower
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enablePower
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_reset
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[18]"></a>SystemInit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, system_mspm0g3507.o(.text.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g3507.o(.text)
</UL>
<P><STRONG><a name="[3f]"></a>delay_cycles</STRONG> (Thumb, 26 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.delay_cycles))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_ack
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_init
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[40]"></a>get_angle</STRONG> (Thumb, 460 bytes, Stack size 64 bytes, hw_jy901s.o(.text.get_angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = get_angle &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; __aeabi_llsr
</UL>
<BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpge
<LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmple
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[51]"></a>jy901s_init</STRONG> (Thumb, 148 bytes, Stack size 56 bytes, hw_jy901s.o(.text.jy901s_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = jy901s_init &rArr; jy901s_write_reg &rArr; i2c_wait_ack &rArr; i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_write_reg
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[41]"></a>jy901s_read_data</STRONG> (Thumb, 220 bytes, Stack size 32 bytes, hw_jy901s.o(.text.jy901s_read_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = jy901s_read_data &rArr; i2c_wait_ack &rArr; i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_ack
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
</UL>

<P><STRONG><a name="[52]"></a>jy901s_write_reg</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, hw_jy901s.o(.text.jy901s_write_reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = jy901s_write_reg &rArr; i2c_wait_ack &rArr; i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_init
</UL>

<P><STRONG><a name="[17]"></a>main</STRONG> (Thumb, 200 bytes, Stack size 72 bytes, empty.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 308<LI>Call Chain = main &rArr; oled_display_attitude &rArr; oled_draw_number &rArr; oled_draw_string &rArr; oled_draw_char &rArr; oled_set_pixel
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_display_attitude
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_angle
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_init
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[57]"></a>oled_clear</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, hw_ssd1306.o(.text.oled_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = oled_clear
</UL>
<BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_display_attitude
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[56]"></a>oled_display_attitude</STRONG> (Thumb, 148 bytes, Stack size 48 bytes, hw_ssd1306.o(.text.oled_display_attitude))
<BR><BR>[Stack]<UL><LI>Max Depth = 236<LI>Call Chain = oled_display_attitude &rArr; oled_draw_number &rArr; oled_draw_string &rArr; oled_draw_char &rArr; oled_set_pixel
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_string
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_update_display
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5b]"></a>oled_draw_char</STRONG> (Thumb, 212 bytes, Stack size 40 bytes, hw_ssd1306.o(.text.oled_draw_char))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = oled_draw_char &rArr; oled_set_pixel
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_set_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_string
</UL>

<P><STRONG><a name="[59]"></a>oled_draw_number</STRONG> (Thumb, 356 bytes, Stack size 104 bytes, hw_ssd1306.o(.text.oled_draw_number))
<BR><BR>[Stack]<UL><LI>Max Depth = 188<LI>Call Chain = oled_draw_number &rArr; oled_draw_string &rArr; oled_draw_char &rArr; oled_set_pixel
</UL>
<BR>[Calls]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idivmod
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_idiv
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_string
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_display_attitude
</UL>

<P><STRONG><a name="[58]"></a>oled_draw_string</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, hw_ssd1306.o(.text.oled_draw_string))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = oled_draw_string &rArr; oled_draw_char &rArr; oled_set_pixel
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_char
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_number
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_display_attitude
</UL>

<P><STRONG><a name="[55]"></a>oled_init</STRONG> (Thumb, 284 bytes, Stack size 32 bytes, hw_ssd1306.o(.text.oled_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = oled_init &rArr; oled_update_display &rArr; oled_write_data &rArr; oled_i2c_wait_ack &rArr; oled_i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_update_display
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_set_pixel
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_clear
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_cmd
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5c]"></a>oled_set_pixel</STRONG> (Thumb, 120 bytes, Stack size 12 bytes, hw_ssd1306.o(.text.oled_set_pixel))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = oled_set_pixel
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_draw_char
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[5a]"></a>oled_update_display</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, hw_ssd1306.o(.text.oled_update_display))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = oled_update_display &rArr; oled_write_data &rArr; oled_i2c_wait_ack &rArr; oled_i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_display_attitude
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[6b]"></a>oled_write_cmd</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, hw_ssd1306.o(.text.oled_write_cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = oled_write_cmd &rArr; oled_i2c_wait_ack &rArr; oled_i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_update_display
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_init
</UL>

<P><STRONG><a name="[6c]"></a>oled_write_data</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, hw_ssd1306.o(.text.oled_write_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = oled_write_data &rArr; oled_i2c_wait_ack &rArr; oled_i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_update_display
</UL>

<P><STRONG><a name="[32]"></a>__ARM_clz</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, depilogue.o(i.__ARM_clz))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[7d]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[7e]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[7f]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[53]"></a>DL_GPIO_setPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, empty.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[54]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, empty.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4e]"></a>i2c_start</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, hw_jy901s.o(.text.i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = i2c_start &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_write_reg
</UL>

<P><STRONG><a name="[4d]"></a>i2c_send_byte</STRONG> (Thumb, 168 bytes, Stack size 48 bytes, hw_jy901s.o(.text.i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = i2c_send_byte &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_write_reg
</UL>

<P><STRONG><a name="[50]"></a>i2c_wait_ack</STRONG> (Thumb, 200 bytes, Stack size 48 bytes, hw_jy901s.o(.text.i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = i2c_wait_ack &rArr; i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInput
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_write_reg
</UL>

<P><STRONG><a name="[4f]"></a>i2c_stop</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, hw_jy901s.o(.text.i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_write_reg
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
</UL>

<P><STRONG><a name="[4b]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, hw_jy901s.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_ack
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[48]"></a>DL_GPIO_setPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, hw_jy901s.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_ack
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[4c]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, hw_jy901s.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_ack
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[47]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, hw_jy901s.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_ack
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_stop
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_send_byte
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_start
</UL>

<P><STRONG><a name="[46]"></a>DL_GPIO_initDigitalInput</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, hw_jy901s.o(.text.DL_GPIO_initDigitalInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalInput
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
</UL>

<P><STRONG><a name="[49]"></a>DL_GPIO_readPins</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, hw_jy901s.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_read_byte
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_wait_ack
</UL>

<P><STRONG><a name="[45]"></a>i2c_read_byte</STRONG> (Thumb, 164 bytes, Stack size 40 bytes, hw_jy901s.o(.text.i2c_read_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2c_read_byte &rArr; DL_GPIO_readPins
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInput
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
</UL>

<P><STRONG><a name="[4a]"></a>i2c_send_ack</STRONG> (Thumb, 144 bytes, Stack size 40 bytes, hw_jy901s.o(.text.i2c_send_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = i2c_send_ack &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jy901s_read_data
</UL>

<P><STRONG><a name="[66]"></a>oled_i2c_start</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, hw_ssd1306.o(.text.oled_i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = oled_i2c_start &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_cmd
</UL>

<P><STRONG><a name="[61]"></a>oled_i2c_send_byte</STRONG> (Thumb, 168 bytes, Stack size 40 bytes, hw_ssd1306.o(.text.oled_i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = oled_i2c_send_byte &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_cmd
</UL>

<P><STRONG><a name="[68]"></a>oled_i2c_wait_ack</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, hw_ssd1306.o(.text.oled_i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = oled_i2c_wait_ack &rArr; oled_i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_readPins
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInput
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_cmd
</UL>

<P><STRONG><a name="[67]"></a>oled_i2c_stop</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, hw_ssd1306.o(.text.oled_i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = oled_i2c_stop &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_cycles
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_write_cmd
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
</UL>

<P><STRONG><a name="[62]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, hw_ssd1306.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
</UL>

<P><STRONG><a name="[63]"></a>DL_GPIO_setPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, hw_ssd1306.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
</UL>

<P><STRONG><a name="[64]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, hw_ssd1306.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
</UL>

<P><STRONG><a name="[65]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, hw_ssd1306.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_stop
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_send_byte
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_start
</UL>

<P><STRONG><a name="[69]"></a>DL_GPIO_initDigitalInput</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, hw_ssd1306.o(.text.DL_GPIO_initDigitalInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalInput
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
</UL>

<P><STRONG><a name="[6a]"></a>DL_GPIO_readPins</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, hw_ssd1306.o(.text.DL_GPIO_readPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_readPins
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_i2c_wait_ack
</UL>

<P><STRONG><a name="[3d]"></a>DL_GPIO_reset</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[3e]"></a>DL_GPIO_enablePower</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[36]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[37]"></a>DL_GPIO_setPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[38]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[39]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
