# OLED引脚更改说明

## 🔄 **引脚变更内容**

### 变更前后对比
| 功能 | 原引脚 | 新引脚 | 变更说明 |
|------|--------|--------|----------|
| OLED SDA | PA15 | **PA12** | I2C数据线 |
| OLED SCL | PA16 | **PA13** | I2C时钟线 |

### 保持不变的引脚
| 功能 | 引脚 | 说明 |
|------|------|------|
| JY901S SDA | PB9 | 传感器I2C数据线 |
| JY901S SCL | PA18 | 传感器I2C时钟线 |
| 调试SWDIO | PA19 | 调试数据线 |
| 调试SWCLK | PA20 | 调试时钟线 |

## 🔧 **修改的文件列表**

### 1. 配置文件
- ✅ **`ti_msp_dl_config.h`**
  - 添加了 `DL_GPIO_PIN_12` 和 `DL_GPIO_PIN_13` 定义
  - 添加了 `IOMUX_PINCM13` 和 `IOMUX_PINCM14` 定义
  - 更新了 `OLED_I2C_SDA_PIN` 和 `OLED_I2C_SCL_PIN` 宏定义

### 2. 文档文件
- ✅ **`硬件连接说明.md`**
  - 更新了引脚分配表
  - 更新了连接步骤说明
  - 更新了连接示意图

- ✅ **`README_Keil5.md`**
  - 更新了引脚分配部分
  - 更新了快速连接表

## 🔌 **新的硬件连接方案**

### 完整连接表
```
LP-MSPM0G3507 LaunchPad 连接方案：

电源连接：
├── 3.3V ──┬── JY901S VCC
│          └── OLED VCC
└── GND  ──┬── JY901S GND
           └── OLED GND

JY901S传感器：
├── SDA → PB9 (Port B, Pin 9)
└── SCL → PA18 (Port A, Pin 18)

OLED显示屏：
├── SDA → PA12 (Port A, Pin 12) ← 新引脚
└── SCL → PA13 (Port A, Pin 13) ← 新引脚

调试接口：
├── SWDIO → PA19
└── SWCLK → PA20
```

### 连接示意图
```
    LP-MSPM0G3507 LaunchPad
    ┌─────────────────────────┐
    │                         │
    │  PA12 ●────────────● SDA│  OLED
    │  PA13 ●────────────● SCL│  (SSD1306)
    │  3.3V ●────────────● VCC│  0.96"
    │   GND ●────────────● GND│
    │                         │
    │  PB9  ●────────────● SDA│  JY901S
    │  PA18 ●────────────● SCL│  九轴传感器
    │  3.3V ●────────────● VCC│
    │   GND ●────────────● GND│
    │                         │
    │  PA19 ●─── SWDIO        │  调试接口
    │  PA20 ●─── SWCLK        │  (XDS110)
    │                         │
    └─────────────────────────┘
```

## ✅ **验证检查**

### 编译验证
- ✅ **配置文件**: 新的引脚宏定义正确
- ✅ **驱动代码**: OLED驱动自动使用新的引脚宏
- ✅ **编译状态**: 无编译错误和警告

### 引脚冲突检查
- ✅ **PA12**: 无冲突，可用于OLED SDA
- ✅ **PA13**: 无冲突，可用于OLED SCL
- ✅ **独立I2C**: JY901S和OLED仍使用独立的I2C总线

## 🚀 **使用说明**

### 重新连接硬件
如果您之前已经连接了硬件，需要重新连接OLED：

1. **断电**: 确保开发板断电
2. **重新连接OLED**:
   - OLED SDA: 从PA15改接到 **PA12**
   - OLED SCL: 从PA16改接到 **PA13**
3. **保持其他连接不变**: JY901S和电源连接无需改动
4. **上电测试**: 重新编译并下载程序

### 编译和测试
1. **重新编译**: 在Keil5中重新编译项目
2. **下载程序**: 将程序下载到开发板
3. **功能测试**: 验证OLED显示是否正常
4. **角度测试**: 旋转JY901S观察角度变化

## 📋 **技术说明**

### 引脚选择原因
- **PA12/PA13**: 这两个引脚在MSPM0G3507上可用且无特殊功能冲突
- **连续引脚**: PA12和PA13是连续的引脚，便于PCB布线
- **GPIO功能**: 支持标准GPIO功能，适合软件I2C实现

### 兼容性说明
- **软件兼容**: 代码无需修改，自动使用新的引脚宏定义
- **硬件兼容**: 新引脚具有相同的电气特性
- **功能兼容**: I2C通信功能完全相同

## ⚠️ **注意事项**

### 硬件连接
1. **断电操作**: 所有引脚更改必须在断电状态下进行
2. **连接确认**: 确保新的连接牢固可靠
3. **电源检查**: 重新确认3.3V和GND连接正确

### 软件配置
1. **重新编译**: 必须重新编译项目以使用新的引脚配置
2. **下载程序**: 重新下载程序到开发板
3. **功能测试**: 验证OLED显示和JY901S数据读取都正常

## 🎯 **预期结果**

更改完成后，系统应该：
- ✅ **OLED正常显示**: 使用PA12/PA13引脚正常工作
- ✅ **JY901S正常工作**: 继续使用PB9/PA18引脚
- ✅ **数据实时更新**: OLED实时显示姿态角度数据
- ✅ **系统稳定**: 10Hz刷新频率，稳定运行

---

**引脚更改完成**: ✅ **OLED引脚已从PA15/PA16更改为PA12/PA13**  
**状态**: 可以重新连接硬件并测试功能
