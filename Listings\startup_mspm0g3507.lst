


ARM Macro Assembler    Page 1 


    1 00000000         ;/******************************************************
                       ***********************
    2 00000000         ; * @file     startup_mspm0g3507.s
    3 00000000         ; * @brief    CMSIS Cortex-M0+ Core Device Startup File 
                       for MSPM0G3507
    4 00000000         ; * @version  V1.0.0
    5 00000000         ; * @date     2024-06-29
    6 00000000         ; ******************************************************
                       ************************/
    7 00000000         
    8 00000000                 PRESERVE8
    9 00000000                 THUMB
   10 00000000         
   11 00000000         ; Vector Table Mapped to Address 0 at Reset
   12 00000000                 AREA             RESET, DATA, READONLY
   13 00000000                 EXPORT           __Vectors
   14 00000000                 EXPORT           __Vectors_End
   15 00000000                 EXPORT           __Vectors_Size
   16 00000000         
   17 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   18 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   19 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   20 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   21 00000010 00000000        DCD              0           ; Reserved
   22 00000014 00000000        DCD              0           ; Reserved
   23 00000018 00000000        DCD              0           ; Reserved
   24 0000001C 00000000        DCD              0           ; Reserved
   25 00000020 00000000        DCD              0           ; Reserved
   26 00000024 00000000        DCD              0           ; Reserved
   27 00000028 00000000        DCD              0           ; Reserved
   28 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   29 00000030 00000000        DCD              0           ; Reserved
   30 00000034 00000000        DCD              0           ; Reserved
   31 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   32 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   33 00000040         
   34 00000040         ; External Interrupts
   35 00000040 00000000        DCD              GROUP0_IRQHandler 
                                                            ; GROUP0 interrupt
   36 00000044 00000000        DCD              GROUP1_IRQHandler 
                                                            ; GROUP1 interrupt
   37 00000048 00000000        DCD              TIMG8_IRQHandler 
                                                            ; TIMG8 interrupt
   38 0000004C 00000000        DCD              UART_INST_IRQHandler ; UART ins
                                                            tance interrupt
   39 00000050 00000000        DCD              ADC0_IRQHandler 
                                                            ; ADC0 interrupt
   40 00000054 00000000        DCD              ADC1_IRQHandler 
                                                            ; ADC1 interrupt
   41 00000058 00000000        DCD              CANFD_IRQHandler 
                                                            ; CANFD interrupt
   42 0000005C 00000000        DCD              DAC_IRQHandler ; DAC interrupt
   43 00000060 00000000        DCD              SPI_INST_IRQHandler ; SPI insta
                                                            nce interrupt



ARM Macro Assembler    Page 2 


   44 00000064 00000000        DCD              I2C_INST_IRQHandler ; I2C insta
                                                            nce interrupt
   45 00000068 00000000        DCD              TIMG0_IRQHandler 
                                                            ; TIMG0 interrupt
   46 0000006C 00000000        DCD              TIMG6_IRQHandler 
                                                            ; TIMG6 interrupt
   47 00000070 00000000        DCD              TIMA0_IRQHandler 
                                                            ; TIMA0 interrupt
   48 00000074 00000000        DCD              TIMA1_IRQHandler 
                                                            ; TIMA1 interrupt
   49 00000078 00000000        DCD              TIMG7_IRQHandler 
                                                            ; TIMG7 interrupt
   50 0000007C 00000000        DCD              TIMG12_IRQHandler 
                                                            ; TIMG12 interrupt
   51 00000080 00000000        DCD              TIMG14_IRQHandler 
                                                            ; TIMG14 interrupt
   52 00000084         
   53 00000084         __Vectors_End
   54 00000084         
   55 00000084 00000084 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
   56 00000084         
   57 00000084                 AREA             |.text|, CODE, READONLY
   58 00000000         
   59 00000000         ; Reset handler
   60 00000000         Reset_Handler
                               PROC
   61 00000000                 EXPORT           Reset_Handler             [WEAK
]
   62 00000000                 IMPORT           SystemInit
   63 00000000                 IMPORT           __main
   64 00000000 4807            LDR              R0, =SystemInit
   65 00000002 4780            BLX              R0
   66 00000004 4807            LDR              R0, =__main
   67 00000006 4700            BX               R0
   68 00000008                 ENDP
   69 00000008         
   70 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
   71 00000008         
   72 00000008         NMI_Handler
                               PROC
   73 00000008                 EXPORT           NMI_Handler                [WEA
K]
   74 00000008 E7FE            B                .
   75 0000000A                 ENDP
   77 0000000A         HardFault_Handler
                               PROC
   78 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
   79 0000000A E7FE            B                .
   80 0000000C                 ENDP
   81 0000000C         SVC_Handler
                               PROC
   82 0000000C                 EXPORT           SVC_Handler                [WEA
K]
   83 0000000C E7FE            B                .
   84 0000000E                 ENDP



ARM Macro Assembler    Page 3 


   85 0000000E         PendSV_Handler
                               PROC
   86 0000000E                 EXPORT           PendSV_Handler             [WEA
K]
   87 0000000E E7FE            B                .
   88 00000010                 ENDP
   89 00000010         SysTick_Handler
                               PROC
   90 00000010                 EXPORT           SysTick_Handler            [WEA
K]
   91 00000010 E7FE            B                .
   92 00000012                 ENDP
   93 00000012         
   94 00000012         Default_Handler
                               PROC
   95 00000012                 EXPORT           GROUP0_IRQHandler          [WEA
K]
   96 00000012                 EXPORT           GROUP1_IRQHandler          [WEA
K]
   97 00000012                 EXPORT           TIMG8_IRQHandler           [WEA
K]
   98 00000012                 EXPORT           UART_INST_IRQHandler       [WEA
K]
   99 00000012                 EXPORT           ADC0_IRQHandler            [WEA
K]
  100 00000012                 EXPORT           ADC1_IRQHandler            [WEA
K]
  101 00000012                 EXPORT           CANFD_IRQHandler           [WEA
K]
  102 00000012                 EXPORT           DAC_IRQHandler             [WEA
K]
  103 00000012                 EXPORT           SPI_INST_IRQHandler        [WEA
K]
  104 00000012                 EXPORT           I2C_INST_IRQHandler        [WEA
K]
  105 00000012                 EXPORT           TIMG0_IRQHandler           [WEA
K]
  106 00000012                 EXPORT           TIMG6_IRQHandler           [WEA
K]
  107 00000012                 EXPORT           TIMA0_IRQHandler           [WEA
K]
  108 00000012                 EXPORT           TIMA1_IRQHandler           [WEA
K]
  109 00000012                 EXPORT           TIMG7_IRQHandler           [WEA
K]
  110 00000012                 EXPORT           TIMG12_IRQHandler          [WEA
K]
  111 00000012                 EXPORT           TIMG14_IRQHandler          [WEA
K]
  112 00000012         
  113 00000012         GROUP0_IRQHandler
  114 00000012         GROUP1_IRQHandler
  115 00000012         TIMG8_IRQHandler
  116 00000012         UART_INST_IRQHandler
  117 00000012         ADC0_IRQHandler
  118 00000012         ADC1_IRQHandler
  119 00000012         CANFD_IRQHandler
  120 00000012         DAC_IRQHandler
  121 00000012         SPI_INST_IRQHandler



ARM Macro Assembler    Page 4 


  122 00000012         I2C_INST_IRQHandler
  123 00000012         TIMG0_IRQHandler
  124 00000012         TIMG6_IRQHandler
  125 00000012         TIMA0_IRQHandler
  126 00000012         TIMA1_IRQHandler
  127 00000012         TIMG7_IRQHandler
  128 00000012         TIMG12_IRQHandler
  129 00000012         TIMG14_IRQHandler
  130 00000012 E7FE            B                .
  131 00000014         
  132 00000014                 ENDP
  133 00000014         
  134 00000014                 ALIGN
  135 00000014         
  136 00000014         ; User heap and stack initialization function (for non-M
                       icroLib)
  137 00000014                 IF               :DEF:__MICROLIB
  140 00000014         
  141 00000014                 IMPORT           __use_two_region_memory
  142 00000014                 EXPORT           __user_initial_stackheap
  143 00000014         
  144 00000014         __user_initial_stackheap
  145 00000014         
  146 00000014 4804            LDR              R0, =  Heap_Mem
  147 00000016 4905            LDR              R1, =(Stack_Mem + 0x400)
  148 00000018 4A05            LDR              R2, = (Heap_Mem + 0x400)
  149 0000001A 4B06            LDR              R3, = Stack_Mem
  150 0000001C 4770            BX               LR
  151 0000001E         
  152 0000001E 00 00           ALIGN
  153 00000020         
  154 00000020                 ENDIF
  155 00000020         
  156 00000020         ;*******************************************************
                       ************************
  157 00000020         ; User Stack and Heap memory areas
  158 00000020         ;*******************************************************
                       ************************
  159 00000020 00000000 
              00000000 
              00000000 
              00000400 
              00000400 
              00000000         AREA             STACK, NOINIT, READWRITE, ALIGN
=3
  160 00000000         Stack_Mem
                               SPACE            0x400
  161 00000400         __initial_sp
  162 00000400         
  163 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
  164 00000000         __heap_base
  165 00000000         Heap_Mem
                               SPACE            0x400
  166 00000400         __heap_limit
  167 00000400         
  168 00000400                 EXPORT           __initial_sp
  169 00000400         
  170 00000400                 IF               :DEF:__MICROLIB



ARM Macro Assembler    Page 5 


  173                          ENDIF
  174 00000400         
  175 00000400                 END
Command Line: --debug --xref --diag_suppress=9931,A1950W --cpu=Cortex-M0+ --dep
end=.\objects\startup_mspm0g3507.d -o.\objects\startup_mspm0g3507.o -ID:\Keil\A
RM\PACK\TexasInstruments\MSPM0G1X0X_G3X0X_DFP\1.3.1\Device\Include --predefine=
"__UVISION_VERSION SETA 540" --predefine="__MSPM0G3507__ SETA 1" --list=.\listi
ngs\startup_mspm0g3507.lst startup_mspm0g3507.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 12 in file startup_mspm0g3507.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 17 in file startup_mspm0g3507.s
   Uses
      At line 13 in file startup_mspm0g3507.s
      At line 55 in file startup_mspm0g3507.s

__Vectors_End 00000084

Symbol: __Vectors_End
   Definitions
      At line 53 in file startup_mspm0g3507.s
   Uses
      At line 14 in file startup_mspm0g3507.s
      At line 55 in file startup_mspm0g3507.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 57 in file startup_mspm0g3507.s
   Uses
      None
Comment: .text unused
ADC0_IRQHandler 00000012

Symbol: ADC0_IRQHandler
   Definitions
      At line 117 in file startup_mspm0g3507.s
   Uses
      At line 39 in file startup_mspm0g3507.s
      At line 99 in file startup_mspm0g3507.s

ADC1_IRQHandler 00000012

Symbol: ADC1_IRQHandler
   Definitions
      At line 118 in file startup_mspm0g3507.s
   Uses
      At line 40 in file startup_mspm0g3507.s
      At line 100 in file startup_mspm0g3507.s

CANFD_IRQHandler 00000012

Symbol: CANFD_IRQHandler
   Definitions
      At line 119 in file startup_mspm0g3507.s
   Uses
      At line 41 in file startup_mspm0g3507.s
      At line 101 in file startup_mspm0g3507.s

DAC_IRQHandler 00000012

Symbol: DAC_IRQHandler
   Definitions
      At line 120 in file startup_mspm0g3507.s
   Uses
      At line 42 in file startup_mspm0g3507.s
      At line 102 in file startup_mspm0g3507.s

Default_Handler 00000012

Symbol: Default_Handler
   Definitions
      At line 94 in file startup_mspm0g3507.s
   Uses
      None
Comment: Default_Handler unused
GROUP0_IRQHandler 00000012

Symbol: GROUP0_IRQHandler
   Definitions
      At line 113 in file startup_mspm0g3507.s
   Uses
      At line 35 in file startup_mspm0g3507.s



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 95 in file startup_mspm0g3507.s

GROUP1_IRQHandler 00000012

Symbol: GROUP1_IRQHandler
   Definitions
      At line 114 in file startup_mspm0g3507.s
   Uses
      At line 36 in file startup_mspm0g3507.s
      At line 96 in file startup_mspm0g3507.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 77 in file startup_mspm0g3507.s
   Uses
      At line 20 in file startup_mspm0g3507.s
      At line 78 in file startup_mspm0g3507.s

I2C_INST_IRQHandler 00000012

Symbol: I2C_INST_IRQHandler
   Definitions
      At line 122 in file startup_mspm0g3507.s
   Uses
      At line 44 in file startup_mspm0g3507.s
      At line 104 in file startup_mspm0g3507.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 72 in file startup_mspm0g3507.s
   Uses
      At line 19 in file startup_mspm0g3507.s
      At line 73 in file startup_mspm0g3507.s

PendSV_Handler 0000000E

Symbol: PendSV_Handler
   Definitions
      At line 85 in file startup_mspm0g3507.s
   Uses
      At line 31 in file startup_mspm0g3507.s
      At line 86 in file startup_mspm0g3507.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 60 in file startup_mspm0g3507.s
   Uses
      At line 18 in file startup_mspm0g3507.s
      At line 61 in file startup_mspm0g3507.s

SPI_INST_IRQHandler 00000012

Symbol: SPI_INST_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 121 in file startup_mspm0g3507.s
   Uses
      At line 43 in file startup_mspm0g3507.s
      At line 103 in file startup_mspm0g3507.s

SVC_Handler 0000000C

Symbol: SVC_Handler
   Definitions
      At line 81 in file startup_mspm0g3507.s
   Uses
      At line 28 in file startup_mspm0g3507.s
      At line 82 in file startup_mspm0g3507.s

SysTick_Handler 00000010

Symbol: SysTick_Handler
   Definitions
      At line 89 in file startup_mspm0g3507.s
   Uses
      At line 32 in file startup_mspm0g3507.s
      At line 90 in file startup_mspm0g3507.s

TIMA0_IRQHandler 00000012

Symbol: TIMA0_IRQHandler
   Definitions
      At line 125 in file startup_mspm0g3507.s
   Uses
      At line 47 in file startup_mspm0g3507.s
      At line 107 in file startup_mspm0g3507.s

TIMA1_IRQHandler 00000012

Symbol: TIMA1_IRQHandler
   Definitions
      At line 126 in file startup_mspm0g3507.s
   Uses
      At line 48 in file startup_mspm0g3507.s
      At line 108 in file startup_mspm0g3507.s

TIMG0_IRQHandler 00000012

Symbol: TIMG0_IRQHandler
   Definitions
      At line 123 in file startup_mspm0g3507.s
   Uses
      At line 45 in file startup_mspm0g3507.s
      At line 105 in file startup_mspm0g3507.s

TIMG12_IRQHandler 00000012

Symbol: TIMG12_IRQHandler
   Definitions
      At line 128 in file startup_mspm0g3507.s
   Uses
      At line 50 in file startup_mspm0g3507.s
      At line 110 in file startup_mspm0g3507.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


TIMG14_IRQHandler 00000012

Symbol: TIMG14_IRQHandler
   Definitions
      At line 129 in file startup_mspm0g3507.s
   Uses
      At line 51 in file startup_mspm0g3507.s
      At line 111 in file startup_mspm0g3507.s

TIMG6_IRQHandler 00000012

Symbol: TIMG6_IRQHandler
   Definitions
      At line 124 in file startup_mspm0g3507.s
   Uses
      At line 46 in file startup_mspm0g3507.s
      At line 106 in file startup_mspm0g3507.s

TIMG7_IRQHandler 00000012

Symbol: TIMG7_IRQHandler
   Definitions
      At line 127 in file startup_mspm0g3507.s
   Uses
      At line 49 in file startup_mspm0g3507.s
      At line 109 in file startup_mspm0g3507.s

TIMG8_IRQHandler 00000012

Symbol: TIMG8_IRQHandler
   Definitions
      At line 115 in file startup_mspm0g3507.s
   Uses
      At line 37 in file startup_mspm0g3507.s
      At line 97 in file startup_mspm0g3507.s

UART_INST_IRQHandler 00000012

Symbol: UART_INST_IRQHandler
   Definitions
      At line 116 in file startup_mspm0g3507.s
   Uses
      At line 38 in file startup_mspm0g3507.s
      At line 98 in file startup_mspm0g3507.s

__user_initial_stackheap 00000014

Symbol: __user_initial_stackheap
   Definitions
      At line 144 in file startup_mspm0g3507.s
   Uses
      At line 142 in file startup_mspm0g3507.s
Comment: __user_initial_stackheap used once
26 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 159 in file startup_mspm0g3507.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 160 in file startup_mspm0g3507.s
   Uses
      At line 147 in file startup_mspm0g3507.s
      At line 149 in file startup_mspm0g3507.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 161 in file startup_mspm0g3507.s
   Uses
      At line 17 in file startup_mspm0g3507.s
      At line 168 in file startup_mspm0g3507.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 163 in file startup_mspm0g3507.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 165 in file startup_mspm0g3507.s
   Uses
      At line 146 in file startup_mspm0g3507.s
      At line 148 in file startup_mspm0g3507.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 164 in file startup_mspm0g3507.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 166 in file startup_mspm0g3507.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

__Vectors_Size 00000084

Symbol: __Vectors_Size
   Definitions
      At line 55 in file startup_mspm0g3507.s
   Uses
      At line 15 in file startup_mspm0g3507.s
Comment: __Vectors_Size used once
1 symbol



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 62 in file startup_mspm0g3507.s
   Uses
      At line 64 in file startup_mspm0g3507.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 63 in file startup_mspm0g3507.s
   Uses
      At line 66 in file startup_mspm0g3507.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 141 in file startup_mspm0g3507.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
378 symbols in table
