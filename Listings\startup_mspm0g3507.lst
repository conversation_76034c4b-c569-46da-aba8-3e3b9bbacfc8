


ARM Macro Assembler    Page 1 


    1 00000000         ;/******************************************************
                       ***********************
    2 00000000         ; * @file     startup_mspm0g3507.s
    3 00000000         ; * @brief    CMSIS Cortex-M0+ Core Device Startup File 
                       for MSPM0G3507
    4 00000000         ; * @version  V1.0.0
    5 00000000         ; * @date     2024-06-29
    6 00000000         ; ******************************************************
                       ************************/
    7 00000000         
    8 00000000                 PRESERVE8
    9 00000000                 THUMB
   10 00000000         
   11 00000000         ; Vector Table Mapped to Address 0 at Reset
   12 00000000                 AREA             RESET, DATA, READONLY
   13 00000000                 EXPORT           __Vectors
   14 00000000                 EXPORT           __Vectors_End
   15 00000000                 EXPORT           __Vectors_Size
   16 00000000         
   17 00000000         __Vectors
                               DCD              __initial_sp ; Top of Stack
   18 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   19 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   20 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   21 00000010 00000000        DCD              0           ; Reserved
   22 00000014 00000000        DCD              0           ; Reserved
   23 00000018 00000000        DCD              0           ; Reserved
   24 0000001C 00000000        DCD              0           ; Reserved
   25 00000020 00000000        DCD              0           ; Reserved
   26 00000024 00000000        DCD              0           ; Reserved
   27 00000028 00000000        DCD              0           ; Reserved
   28 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   29 00000030 00000000        DCD              0           ; Reserved
   30 00000034 00000000        DCD              0           ; Reserved
   31 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   32 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   33 00000040         
   34 00000040         ; External Interrupts
   35 00000040 00000000        DCD              GROUP0_IRQHandler 
                                                            ; GROUP0 interrupt
   36 00000044 00000000        DCD              GROUP1_IRQHandler 
                                                            ; GROUP1 interrupt
   37 00000048 00000000        DCD              TIMG8_IRQHandler 
                                                            ; TIMG8 interrupt
   38 0000004C 00000000        DCD              UART_INST_IRQHandler ; UART ins
                                                            tance interrupt
   39 00000050 00000000        DCD              ADC0_IRQHandler 
                                                            ; ADC0 interrupt
   40 00000054 00000000        DCD              ADC1_IRQHandler 
                                                            ; ADC1 interrupt
   41 00000058 00000000        DCD              CANFD_IRQHandler 
                                                            ; CANFD interrupt
   42 0000005C 00000000        DCD              DAC_IRQHandler ; DAC interrupt
   43 00000060 00000000        DCD              SPI_INST_IRQHandler ; SPI insta
                                                            nce interrupt
   44 00000064 00000000        DCD              I2C_INST_IRQHandler ; I2C insta



ARM Macro Assembler    Page 2 


                                                            nce interrupt
   45 00000068 00000000        DCD              TIMG0_IRQHandler 
                                                            ; TIMG0 interrupt
   46 0000006C 00000000        DCD              TIMG6_IRQHandler 
                                                            ; TIMG6 interrupt
   47 00000070 00000000        DCD              TIMA0_IRQHandler 
                                                            ; TIMA0 interrupt
   48 00000074 00000000        DCD              TIMA1_IRQHandler 
                                                            ; TIMA1 interrupt
   49 00000078 00000000        DCD              TIMG7_IRQHandler 
                                                            ; TIMG7 interrupt
   50 0000007C 00000000        DCD              TIMG12_IRQHandler 
                                                            ; TIMG12 interrupt
   51 00000080 00000000        DCD              TIMG14_IRQHandler 
                                                            ; TIMG14 interrupt
   52 00000084         
   53 00000084         __Vectors_End
   54 00000084         
   55 00000084 00000084 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
   56 00000084         
   57 00000084                 AREA             |.text|, CODE, READONLY
   58 00000000         
   59 00000000         ; Reset handler
   60 00000000         Reset_Handler
                               PROC
   61 00000000                 EXPORT           Reset_Handler             [WEAK
]
   62 00000000                 IMPORT           SystemInit
   63 00000000                 IMPORT           __main
   64 00000000 4807            LDR              R0, =SystemInit
   65 00000002 4780            BLX              R0
   66 00000004 4807            LDR              R0, =__main
   67 00000006 4700            BX               R0
   68 00000008                 ENDP
   69 00000008         
   70 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
   71 00000008         
   72 00000008         NMI_Handler
                               PROC
   73 00000008                 EXPORT           NMI_Handler                [WEA
K]
   74 00000008 E7FE            B                .
   75 0000000A                 ENDP
   77 0000000A         HardFault_Handler
                               PROC
   78 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
   79 0000000A E7FE            B                .
   80 0000000C                 ENDP
   81 0000000C         SVC_Handler
                               PROC
   82 0000000C                 EXPORT           SVC_Handler                [WEA
K]
   83 0000000C E7FE            B                .
   84 0000000E                 ENDP
   85 0000000E         PendSV_Handler



ARM Macro Assembler    Page 3 


                               PROC
   86 0000000E                 EXPORT           PendSV_Handler             [WEA
K]
   87 0000000E E7FE            B                .
   88 00000010                 ENDP
   89 00000010         SysTick_Handler
                               PROC
   90 00000010                 EXPORT           SysTick_Handler            [WEA
K]
   91 00000010 E7FE            B                .
   92 00000012                 ENDP
   93 00000012         
   94 00000012         Default_Handler
                               PROC
   95 00000012                 EXPORT           GROUP0_IRQHandler          [WEA
K]
   96 00000012                 EXPORT           GROUP1_IRQHandler          [WEA
K]
   97 00000012                 EXPORT           TIMG8_IRQHandler           [WEA
K]
   98 00000012                 EXPORT           UART_INST_IRQHandler       [WEA
K]
   99 00000012                 EXPORT           ADC0_IRQHandler            [WEA
K]
  100 00000012                 EXPORT           ADC1_IRQHandler            [WEA
K]
  101 00000012                 EXPORT           CANFD_IRQHandler           [WEA
K]
  102 00000012                 EXPORT           DAC_IRQHandler             [WEA
K]
  103 00000012                 EXPORT           SPI_INST_IRQHandler        [WEA
K]
  104 00000012                 EXPORT           I2C_INST_IRQHandler        [WEA
K]
  105 00000012                 EXPORT           TIMG0_IRQHandler           [WEA
K]
  106 00000012                 EXPORT           TIMG6_IRQHandler           [WEA
K]
  107 00000012                 EXPORT           TIMA0_IRQHandler           [WEA
K]
  108 00000012                 EXPORT           TIMA1_IRQHandler           [WEA
K]
  109 00000012                 EXPORT           TIMG7_IRQHandler           [WEA
K]
  110 00000012                 EXPORT           TIMG12_IRQHandler          [WEA
K]
  111 00000012                 EXPORT           TIMG14_IRQHandler          [WEA
K]
  112 00000012         
  113 00000012         GROUP0_IRQHandler
  114 00000012         GROUP1_IRQHandler
  115 00000012         TIMG8_IRQHandler
  116 00000012         UART_INST_IRQHandler
  117 00000012         ADC0_IRQHandler
  118 00000012         ADC1_IRQHandler
  119 00000012         CANFD_IRQHandler
  120 00000012         DAC_IRQHandler
  121 00000012         SPI_INST_IRQHandler
  122 00000012         I2C_INST_IRQHandler



ARM Macro Assembler    Page 4 


  123 00000012         TIMG0_IRQHandler
  124 00000012         TIMG6_IRQHandler
  125 00000012         TIMA0_IRQHandler
  126 00000012         TIMA1_IRQHandler
  127 00000012         TIMG7_IRQHandler
  128 00000012         TIMG12_IRQHandler
  129 00000012         TIMG14_IRQHandler
  130 00000012 E7FE            B                .
  131 00000014         
  132 00000014                 ENDP
  133 00000014         
  134 00000014                 ALIGN
  135 00000014         
  136 00000014         ;*******************************************************
                       ************************
  137 00000014         ; User Stack and Heap initialization
  138 00000014         ;*******************************************************
                       ************************
  139 00000014                 IF               :DEF:__MICROLIB
  146 00000014         
  147 00000014                 IMPORT           __use_two_region_memory
  148 00000014                 EXPORT           __user_initial_stackheap
  149 00000014         
  150 00000014         __user_initial_stackheap
  151 00000014         
  152 00000014 4804            LDR              R0, =  Heap_Mem
  153 00000016 4904            LDR              R1, =(Stack_Mem + Stack_Size)
  154 00000018 4A03            LDR              R2, = (Heap_Mem +  Heap_Size)
  155 0000001A 4B03            LDR              R3, = Stack_Mem
  156 0000001C 4770            BX               LR
  157 0000001E         
  158 0000001E 00 00           ALIGN
  159 00000020         
  160 00000020                 ENDIF
  161 00000020         
  162 00000020                 END
              00000000 
              00000000 
              00000000 
              00000000 
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931,A1950W --cpu=Cortex-M0+ --dep
end=.\objects\startup_mspm0g3507.d -o.\objects\startup_mspm0g3507.o -ID:\Keil\A
RM\PACK\TexasInstruments\MSPM0G1X0X_G3X0X_DFP\1.3.1\Device\Include --predefine=
"__UVISION_VERSION SETA 540" --predefine="__MSPM0G3507__ SETA 1" --list=.\listi
ngs\startup_mspm0g3507.lst startup_mspm0g3507.s
